name: main-ci
on:
    pull_request:
        branches:
            - main

jobs:
    main-build:
        runs-on: ubuntu-latest
        permissions:
            contents: write
        steps:
            - name: checkout code
              uses: actions/checkout@v4
              with:
                  ref: ${{ github.head_ref }}
            - name: install node and pnpm
              uses: pnpm/action-setup@v4

            # prepare
            - run: pnpm install

            # build两个版本
            - name: build minify
              run: pnpm run build:minify
            - name: mkdir
              run: mkdir output
            - name: move and rename file
              run: mv dist/bilibili-cleaner.user.js output/bilibili-cleaner.min.user.js
            - name: build default
              run: pnpm run build
            - name: copy file
              run: cp dist/bilibili-cleaner.user.js output/
            - name: add doc
              run: cp LICENSE README.md CHANGELOG.md output/
            - name: list files
              run: ls -lah output/

            # 上传产物
            - name: upload-artifact
              uses: actions/upload-artifact@v4
              with:
                  name: bilibili-cleaner
                  path: output/*
