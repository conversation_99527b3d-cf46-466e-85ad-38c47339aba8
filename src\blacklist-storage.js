/**
 * 黑名单存储管理模块
 * 使用localStorage管理黑名单数据
 */

const BLACKLIST_KEY = 'weibo_enhancer_blacklist';

export class BlacklistStorage {
  constructor() {
    this.blacklist = this.loadBlacklist();
  }

  /**
   * 从localStorage加载黑名单
   * @returns {Array} 黑名单数组
   */
  loadBlacklist() {
    try {
      const data = localStorage.getItem(BLACKLIST_KEY);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Failed to load blacklist:', error);
      return [];
    }
  }

  /**
   * 保存黑名单到localStorage
   */
  saveBlacklist() {
    try {
      localStorage.setItem(BLACKLIST_KEY, JSON.stringify(this.blacklist));
      console.log('Blacklist saved successfully');
    } catch (error) {
      console.error('Failed to save blacklist:', error);
    }
  }

  /**
   * 添加用户到黑名单
   * @param {Object} user - 用户信息
   * @param {string} user.userId - 用户ID
   * @param {string} user.username - 用户名
   * @param {string} user.avatar - 头像URL
   * @returns {boolean} 是否添加成功
   */
  addUser(user) {
    if (!user.userId || !user.username) {
      console.error('Invalid user data:', user);
      return false;
    }

    // 检查是否已存在
    if (this.isBlacklisted(user.userId)) {
      console.log('User already in blacklist:', user.username);
      return false;
    }

    const blacklistEntry = {
      userId: user.userId,
      username: user.username,
      avatar: user.avatar || '',
      addedAt: new Date().toISOString()
    };

    this.blacklist.push(blacklistEntry);
    this.saveBlacklist();
    
    console.log('User added to blacklist:', user.username);
    return true;
  }

  /**
   * 从黑名单移除用户
   * @param {string} userId - 用户ID
   * @returns {boolean} 是否移除成功
   */
  removeUser(userId) {
    const initialLength = this.blacklist.length;
    this.blacklist = this.blacklist.filter(user => user.userId !== userId);
    
    if (this.blacklist.length < initialLength) {
      this.saveBlacklist();
      console.log('User removed from blacklist:', userId);
      return true;
    }
    
    return false;
  }

  /**
   * 检查用户是否在黑名单中
   * @param {string} userId - 用户ID
   * @returns {boolean} 是否在黑名单中
   */
  isBlacklisted(userId) {
    return this.blacklist.some(user => user.userId === userId);
  }

  /**
   * 获取黑名单用户信息
   * @param {string} userId - 用户ID
   * @returns {Object|null} 用户信息或null
   */
  getUser(userId) {
    return this.blacklist.find(user => user.userId === userId) || null;
  }

  /**
   * 获取完整黑名单
   * @returns {Array} 黑名单数组
   */
  getAllUsers() {
    return [...this.blacklist];
  }

  /**
   * 清空黑名单
   */
  clear() {
    this.blacklist = [];
    this.saveBlacklist();
    console.log('Blacklist cleared');
  }

  /**
   * 获取黑名单统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      total: this.blacklist.length,
      oldestEntry: this.blacklist.length > 0 ? 
        Math.min(...this.blacklist.map(u => new Date(u.addedAt).getTime())) : null,
      newestEntry: this.blacklist.length > 0 ? 
        Math.max(...this.blacklist.map(u => new Date(u.addedAt).getTime())) : null
    };
  }

  /**
   * 导出黑名单数据
   * @returns {string} JSON字符串
   */
  export() {
    return JSON.stringify(this.blacklist, null, 2);
  }

  /**
   * 导入黑名单数据
   * @param {string} jsonData - JSON字符串
   * @returns {boolean} 是否导入成功
   */
  import(jsonData) {
    try {
      const importedData = JSON.parse(jsonData);
      if (Array.isArray(importedData)) {
        this.blacklist = importedData;
        this.saveBlacklist();
        console.log('Blacklist imported successfully');
        return true;
      } else {
        console.error('Invalid blacklist data format');
        return false;
      }
    } catch (error) {
      console.error('Failed to import blacklist:', error);
      return false;
    }
  }
}
