/**
 * 微博隐藏功能模块
 * 检测并隐藏黑名单用户的微博，支持动态加载
 */

export class PostHider {
  constructor(blacklistStorage, userExtractor) {
    this.blacklistStorage = blacklistStorage;
    this.userExtractor = userExtractor;
    this.observer = null;
    this.hiddenPosts = new Set();
    
    this.init();
  }

  /**
   * 初始化微博隐藏功能
   */
  init() {
    this.hideExistingPosts();
    this.observeNewPosts();
    this.bindEvents();
    console.log('Post hider initialized');
  }

  /**
   * 隐藏当前页面已存在的微博
   */
  hideExistingPosts() {
    const articles = this.userExtractor.getAllArticles();
    let hiddenCount = 0;

    Array.from(articles).forEach(article => {
      if (this.shouldHidePost(article)) {
        this.hidePost(article);
        hiddenCount++;
      }
    });

    if (hiddenCount > 0) {
      console.log(`Hidden ${hiddenCount} existing posts from blacklisted users`);
    }
  }

  /**
   * 监听新加载的微博
   */
  observeNewPosts() {
    this.observer = new MutationObserver((mutations) => {
      let hasNewPosts = false;

      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // 检查是否是微博文章
              if (node.matches && node.matches('article.Feed_wrap_3v9LH')) {
                if (this.shouldHidePost(node)) {
                  this.hidePost(node);
                  hasNewPosts = true;
                }
              }
              
              // 检查子元素中是否有微博文章
              const articles = node.querySelectorAll && node.querySelectorAll('article.Feed_wrap_3v9LH');
              if (articles) {
                Array.from(articles).forEach(article => {
                  if (this.shouldHidePost(article)) {
                    this.hidePost(article);
                    hasNewPosts = true;
                  }
                });
              }
            }
          });
        }
      });

      if (hasNewPosts) {
        console.log('Hidden new posts from blacklisted users');
      }
    });

    // 观察整个文档的变化
    this.observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 监听用户被拉黑事件
    document.addEventListener('userBlacklisted', (e) => {
      const { userInfo } = e.detail;
      this.hideUserPosts(userInfo.userId);
    });

    // 监听用户被取消拉黑事件
    document.addEventListener('userUnblacklisted', (e) => {
      const { userInfo } = e.detail;
      this.showUserPosts(userInfo.userId);
    });
  }

  /**
   * 判断是否应该隐藏微博
   * @param {Element} article - 微博文章DOM元素
   * @returns {boolean} 是否应该隐藏
   */
  shouldHidePost(article) {
    const userId = this.userExtractor.getArticleUserId(article);
    return userId && this.blacklistStorage.isBlacklisted(userId);
  }

  /**
   * 隐藏微博
   * @param {Element} article - 微博文章DOM元素
   */
  hidePost(article) {
    const userId = this.userExtractor.getArticleUserId(article);
    if (!userId) return;

    // 避免重复隐藏
    if (this.hiddenPosts.has(article)) return;

    const userInfo = this.blacklistStorage.getUser(userId);
    const username = userInfo ? userInfo.username : '未知用户';

    // 创建隐藏容器
    const hiddenContainer = document.createElement('div');
    hiddenContainer.className = 'weibo-enhancer-hidden-post';
    hiddenContainer.style.cssText = `
      background: #f5f5f5;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      padding: 16px;
      margin: 8px 0;
      text-align: center;
      color: #666;
      font-size: 14px;
      position: relative;
    `;

    hiddenContainer.innerHTML = `
      <div style="margin-bottom: 8px;">
        <span style="color: #999;">已屏蔽用户: ${username}</span>
      </div>
      <div style="display: flex; gap: 8px; justify-content: center; align-items: center;">
        <button class="show-post-btn" style="
          background: #1890ff;
          color: white;
          border: none;
          padding: 4px 12px;
          border-radius: 3px;
          cursor: pointer;
          font-size: 12px;
        ">显示此微博</button>
        <button class="unblock-user-btn" style="
          background: #52c41a;
          color: white;
          border: none;
          padding: 4px 12px;
          border-radius: 3px;
          cursor: pointer;
          font-size: 12px;
        ">取消屏蔽</button>
      </div>
    `;

    // 绑定按钮事件
    const showBtn = hiddenContainer.querySelector('.show-post-btn');
    const unblockBtn = hiddenContainer.querySelector('.unblock-user-btn');

    showBtn.addEventListener('click', () => {
      this.showPost(article, hiddenContainer);
    });

    unblockBtn.addEventListener('click', () => {
      this.blacklistStorage.removeUser(userId);
      this.showPost(article, hiddenContainer);
      
      // 显示通知
      this.showNotification(`已取消屏蔽: ${username}`, 'success');
      
      // 触发事件
      document.dispatchEvent(new CustomEvent('userUnblacklisted', {
        detail: { userInfo: { userId, username } }
      }));
    });

    // 保存原始文章引用
    hiddenContainer._originalArticle = article;

    // 隐藏原文章并插入隐藏容器
    article.style.display = 'none';
    article.parentNode.insertBefore(hiddenContainer, article);

    this.hiddenPosts.add(article);
    console.log(`Hidden post from user: ${username}`);
  }

  /**
   * 显示微博
   * @param {Element} article - 微博文章DOM元素
   * @param {Element} hiddenContainer - 隐藏容器元素
   */
  showPost(article, hiddenContainer) {
    article.style.display = '';
    if (hiddenContainer && hiddenContainer.parentNode) {
      hiddenContainer.parentNode.removeChild(hiddenContainer);
    }
    this.hiddenPosts.delete(article);
  }

  /**
   * 隐藏指定用户的所有微博
   * @param {string} userId - 用户ID
   */
  hideUserPosts(userId) {
    const articles = this.userExtractor.findArticlesByUserId(userId);
    let hiddenCount = 0;

    articles.forEach(article => {
      if (!this.hiddenPosts.has(article)) {
        this.hidePost(article);
        hiddenCount++;
      }
    });

    if (hiddenCount > 0) {
      console.log(`Hidden ${hiddenCount} posts from user: ${userId}`);
    }
  }

  /**
   * 显示指定用户的所有微博
   * @param {string} userId - 用户ID
   */
  showUserPosts(userId) {
    const hiddenContainers = document.querySelectorAll('.weibo-enhancer-hidden-post');
    let shownCount = 0;

    Array.from(hiddenContainers).forEach(container => {
      const article = container._originalArticle;
      if (article && this.userExtractor.getArticleUserId(article) === userId) {
        this.showPost(article, container);
        shownCount++;
      }
    });

    if (shownCount > 0) {
      console.log(`Shown ${shownCount} posts from user: ${userId}`);
    }
  }

  /**
   * 获取隐藏统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const hiddenContainers = document.querySelectorAll('.weibo-enhancer-hidden-post');
    return {
      hiddenPostsCount: this.hiddenPosts.size,
      hiddenContainersCount: hiddenContainers.length,
      blacklistedUsersCount: this.blacklistStorage.getAllUsers().length
    };
  }

  /**
   * 显示通知
   * @param {string} message - 通知消息
   * @param {string} type - 通知类型
   */
  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'success' ? '#52c41a' : type === 'error' ? '#ff4d4f' : '#1890ff'};
      color: white;
      padding: 12px 20px;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      z-index: 10001;
      font-size: 14px;
      max-width: 300px;
      word-wrap: break-word;
    `;
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  /**
   * 销毁微博隐藏功能
   */
  destroy() {
    if (this.observer) {
      this.observer.disconnect();
    }

    // 显示所有隐藏的微博
    const hiddenContainers = document.querySelectorAll('.weibo-enhancer-hidden-post');
    Array.from(hiddenContainers).forEach(container => {
      const article = container._originalArticle;
      if (article) {
        this.showPost(article, container);
      }
    });

    this.hiddenPosts.clear();
    console.log('Post hider destroyed');
  }
}
