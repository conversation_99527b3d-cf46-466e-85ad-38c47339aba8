/**
 * 用户信息提取模块
 * 从微博DOM结构中提取用户ID、用户名等信息
 */

export class UserExtractor {
  constructor() {
    // 基于hot-weibos-list.html的选择器
    this.selectors = {
      // 用户卡片属性 - 包含用户ID
      usercard: '[usercard]',
      
      // 用户名链接
      usernameLink: '.head_name_24eEB',
      
      // 头像图片
      avatar: '.woo-avatar-img',
      
      // 用户链接 (备用方案)
      userLink: 'a[href*="/u/"]'
    };
  }

  /**
   * 从微博文章中提取用户信息
   * @param {Element} article - 微博文章DOM元素
   * @returns {Object|null} 用户信息对象或null
   */
  extractUserInfo(article) {
    try {
      // 方法1: 通过usercard属性获取用户ID
      const usercardElement = article.querySelector(this.selectors.usercard);
      if (!usercardElement) {
        console.warn('No usercard element found in article');
        return null;
      }

      const userId = usercardElement.getAttribute('usercard');
      if (!userId) {
        console.warn('No usercard attribute found');
        return null;
      }

      // 获取用户名
      const usernameElement = article.querySelector(this.selectors.usernameLink);
      const username = usernameElement ? 
        usernameElement.textContent.trim() : 
        this.extractUsernameFromUrl(article);

      if (!username) {
        console.warn('No username found');
        return null;
      }

      // 获取头像URL
      const avatarElement = article.querySelector(this.selectors.avatar);
      const avatar = avatarElement ? avatarElement.src : '';

      // 获取用户主页链接
      const userLinkElement = article.querySelector(this.selectors.userLink);
      const userUrl = userLinkElement ? userLinkElement.href : `https://weibo.com/u/${userId}`;

      const userInfo = {
        userId,
        username,
        avatar,
        userUrl,
        extractedAt: new Date().toISOString()
      };

      console.log('Extracted user info:', userInfo);
      return userInfo;

    } catch (error) {
      console.error('Failed to extract user info:', error);
      return null;
    }
  }

  /**
   * 从URL中提取用户名 (备用方案)
   * @param {Element} article - 微博文章DOM元素
   * @returns {string} 用户名或空字符串
   */
  extractUsernameFromUrl(article) {
    try {
      const userLink = article.querySelector(this.selectors.userLink);
      if (!userLink) return '';

      // 从aria-label属性获取用户名
      const ariaLabel = userLink.getAttribute('aria-label');
      if (ariaLabel) {
        return ariaLabel.trim();
      }

      // 从title属性获取用户名
      const title = userLink.getAttribute('title');
      if (title) {
        return title.trim();
      }

      // 从链接文本获取用户名
      const linkText = userLink.textContent.trim();
      if (linkText) {
        return linkText;
      }

      return '';
    } catch (error) {
      console.error('Failed to extract username from URL:', error);
      return '';
    }
  }

  /**
   * 验证用户信息是否完整
   * @param {Object} userInfo - 用户信息对象
   * @returns {boolean} 是否有效
   */
  validateUserInfo(userInfo) {
    if (!userInfo) return false;
    
    return !!(userInfo.userId && 
             userInfo.username && 
             userInfo.userId.length > 0 && 
             userInfo.username.length > 0);
  }

  /**
   * 从多个微博文章中批量提取用户信息
   * @param {NodeList|Array} articles - 微博文章DOM元素列表
   * @returns {Array} 用户信息数组
   */
  extractMultipleUsers(articles) {
    const users = [];
    const seenUserIds = new Set();

    Array.from(articles).forEach(article => {
      const userInfo = this.extractUserInfo(article);
      if (userInfo && this.validateUserInfo(userInfo)) {
        // 去重
        if (!seenUserIds.has(userInfo.userId)) {
          users.push(userInfo);
          seenUserIds.add(userInfo.userId);
        }
      }
    });

    return users;
  }

  /**
   * 获取当前页面所有微博文章
   * @returns {NodeList} 微博文章DOM元素列表
   */
  getAllArticles() {
    return document.querySelectorAll('article.Feed_wrap_3v9LH');
  }

  /**
   * 获取当前页面所有用户信息
   * @returns {Array} 用户信息数组
   */
  getAllUsersOnPage() {
    const articles = this.getAllArticles();
    return this.extractMultipleUsers(articles);
  }

  /**
   * 根据用户ID查找页面上的所有相关文章
   * @param {string} userId - 用户ID
   * @returns {Array} 相关文章DOM元素数组
   */
  findArticlesByUserId(userId) {
    const articles = this.getAllArticles();
    const userArticles = [];

    Array.from(articles).forEach(article => {
      const usercardElement = article.querySelector(this.selectors.usercard);
      if (usercardElement && usercardElement.getAttribute('usercard') === userId) {
        userArticles.push(article);
      }
    });

    return userArticles;
  }

  /**
   * 根据用户名查找页面上的所有相关文章
   * @param {string} username - 用户名
   * @returns {Array} 相关文章DOM元素数组
   */
  findArticlesByUsername(username) {
    const articles = this.getAllArticles();
    const userArticles = [];

    Array.from(articles).forEach(article => {
      const usernameElement = article.querySelector(this.selectors.usernameLink);
      if (usernameElement && usernameElement.textContent.trim() === username) {
        userArticles.push(article);
      }
    });

    return userArticles;
  }

  /**
   * 检查文章是否属于指定用户
   * @param {Element} article - 微博文章DOM元素
   * @param {string} userId - 用户ID
   * @returns {boolean} 是否属于该用户
   */
  isArticleByUser(article, userId) {
    const usercardElement = article.querySelector(this.selectors.usercard);
    return usercardElement && usercardElement.getAttribute('usercard') === userId;
  }

  /**
   * 获取文章的用户ID
   * @param {Element} article - 微博文章DOM元素
   * @returns {string|null} 用户ID或null
   */
  getArticleUserId(article) {
    const usercardElement = article.querySelector(this.selectors.usercard);
    return usercardElement ? usercardElement.getAttribute('usercard') : null;
  }

  /**
   * 更新选择器 (用于适配页面变化)
   * @param {Object} newSelectors - 新的选择器对象
   */
  updateSelectors(newSelectors) {
    this.selectors = { ...this.selectors, ...newSelectors };
    console.log('Selectors updated:', this.selectors);
  }
}
