// 默认宽屏播放 刷新生效
html[default-widescreen] {
    // 修复mini播放模式主播放器宽度支撑问题
    &[player-is-wide] #playerWrap:has(.bpx-player-container[data-screen='mini']) {
        width: fit-content;
    }
}

@mixin video-scrollable() {
    .webscreen-fix {
        position: unset;
        top: unset;
        left: unset;
        margin: unset;
        padding: unset;
        width: unset;
        height: unset;

        #biliMainHeader {
            display: none;
        }
        #mirror-vdcon {
            box-sizing: content-box;
            position: relative;
        }

        #danmukuBox .bui-collapse-wrap:not(.bui-collapse-wrap-folded) {
            margin-top: 0 !important;
            .bpx-player-wraplist {
                max-height: calc(50vh + 75px) !important;
            }
            .bpx-player-dm-wrap {
                max-height: 50vh !important;
            }
        }
        .left-container,
        .playlist-container--left {
            position: static !important;
            padding-top: 100vh;

            .video-info-container {
                height: fit-content;
            }
            #bilibili-player.mode-webscreen {
                position: static;
                border-radius: unset;
                z-index: unset;
                left: unset;
                top: unset;
                width: 100%;
                height: 100%;
            }
            #playerWrap {
                position: absolute;
                left: 0;
                right: 0;
                top: 0;
                height: 100vh;
                width: 100vw;
                padding-right: 0;
            }
        }

        .right-container,
        .playlist-container--right {
            padding-top: 100vh;
        }

        // 隐藏小窗
        .float-nav-exp .nav-menu .item.mini,
        .fixed-sidenav-storage .mini-player-window {
            display: none !important;
        }

        // 投币收藏弹窗
        .bili-dialog-m {
            z-index: 100000 !important;
        }

        // 滚动条
        &::-webkit-scrollbar {
            display: none !important;
        }
    }

    // msg bubble
    .bili-msg {
        z-index: 100001 !important;
    }
}

// 网页全屏时 页面可滚动
html[webscreen-scrollable] {
    @include video-scrollable;
}

// 网页全屏时 页面可滚动 firefox滚动条
@supports (-moz-appearance: none) {
    html[webscreen-scrollable] {
        &:has(.webscreen-fix) {
            scrollbar-width: none !important;
        }
    }
}

// 全屏时 页面可滚动
html[fullscreen-scrollable] {
    @include video-scrollable;
}

// 全屏时 页面可滚动 firefox滚动条
@supports (-moz-appearance: none) {
    html[fullscreen-scrollable] {
        &:has(.webscreen-fix) {
            scrollbar-width: none !important;
        }
    }
}

// 全屏滚动时 在视频底部显示顶栏
// issue #199, 参考 https://greasyfork.org/scripts/445241
html[screen-scrollable-move-header-bottom] {
    .webscreen-fix {
        #biliMainHeader {
            display: block !important;
            top: 100vh !important;
            width: 100% !important;
            position: absolute !important;
        }
        .fixed-header .bili-header__bar {
            position: relative !important;
        }
        #mirror-vdcon {
            padding-top: 64px !important;
        }

        // 适配 bilibli evolved
        .custom-navbar[role='navigation'] {
            top: 100vh !important;
            z-index: 1000 !important;
            .custom-navbar-item .popup {
                top: 100% !important;
            }
        }
        &.fixed-navbar .custom-navbar[role='navigation'] {
            position: absolute !important;
        }
    }

    // 动画闪现问题
    .custom-navbar[role='navigation'] {
        transition: unset !important;
    }
}

// 播放器和视频信息 交换位置
html[video-page-exchange-player-position] {
    body:not(.webscreen-fix) :is(.left-container, .playlist-container--left) {
        display: flex !important;
        flex-direction: column !important;
        padding-top: 30px !important;
    }
    body:not(.webscreen-fix) :is(.left-container, .playlist-container--left) > * {
        order: 1;
    }
    body:not(.webscreen-fix) #playerWrap {
        order: 0 !important;
        z-index: 1;
    }
    body:not(.webscreen-fix) .video-info-container {
        height: auto !important;
        padding-top: 16px !important;

        // 高权限消除展开标题的间距
        margin-bottom: 0 !important;
    }

    // fix #80 宽屏模式下播放器遮盖up主
    &[player-is-wide] body:not(.webscreen-fix) .up-panel-container {
        position: relative !important;

        /*
            拟合魔法，勿动
            videoWidth = innerWidth * 0.962339 - 359.514px
            videoHeight = max(min(calc(innerWidth * 0.962339 - 359.514px), 2010px), 923px) * 9/16 + 46px
        */
        margin-top: calc(max(min(calc(100vw * 0.9623 - 359.514px), 2010px), 923px) * 9 / 16 + 46px + 35px);
    }
    &[player-is-wide] body:not(.webscreen-fix) #danmukuBox {
        margin-top: 0 !important;
    }

    // msg bubble
    .bili-msg {
        z-index: 100001 !important;
    }
}

// 普通播放 视频宽度调节
html[normalscreen-width] {
    $normal-width: min(calc(100vw - 400px), var(--normalscreen-width));

    // normal 调节评论宽度、播放器宽度，data-screen未赋值时按normal处理
    &:not([player-is-wide]):has(
            #bilibili-player .bpx-player-container[data-screen='normal'],
            #bilibili-player .bpx-player-container:not([data-screen])
        ) {
        #playerWrap {
            height: fit-content !important;

            // placeholder
            #bilibili-player-placeholder-top {
                width: $normal-width !important;
                aspect-ratio: 16 / 9 !important;
            }

            // player
            #bilibili-player {
                width: $normal-width !important;
                height: fit-content;
                .bpx-player-video-area {
                    width: $normal-width !important;
                    aspect-ratio: 16 / 9 !important;
                }
            }
        }

        // comment
        .left-container,
        .playlist-container--left {
            width: min(calc(100vw - 400px), var(--normalscreen-width)) !important;
        }
    }

    // web + full 全屏滚动时调节评论宽度
    .webscreen-fix {
        .left-container,
        .playlist-container--left {
            width: min(calc(100vw - 400px), var(--normalscreen-width)) !important;
        }
    }

    // mini 仅normal mini模式下调节宽度
    &:not([player-is-wide]):has(.bpx-player-container[data-screen='mini']) {
        .left-container,
        .playlist-container--left {
            width: min(calc(100vw - 400px), var(--normalscreen-width)) !important;
        }

        // 高度填充
        #playerWrap {
            width: $normal-width !important;
            height: fit-content !important;
            display: flex !important;
            #bilibili-player-placeholder {
                position: static;
                width: $normal-width !important;
                height: fit-content !important;
                #bilibili-player-placeholder-top {
                    width: $normal-width !important;
                    aspect-ratio: 16 / 9 !important;
                }
            }
            #bilibili-player {
                width: $normal-width !important;
                height: fit-content !important;
            }
        }
    }

    // wide 仅wide mini模式下调节背景色，去除切换小窗模式时宽度跳变
    &[player-is-wide]:has(.bpx-player-container[data-screen='mini']) {
        #bilibili-player {
            background-color: black !important;
        }
    }
}
