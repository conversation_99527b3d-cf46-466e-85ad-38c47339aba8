import { Item } from '@/types/item'

export const liveHeaderLeftItems: Item[] = [
    {
        type: 'switch',
        id: 'live-page-header-entry-logo',
        name: '隐藏 直播LOGO',
    },
    {
        type: 'switch',
        id: 'live-page-header-entry-title',
        name: '隐藏 首页',
    },
    {
        type: 'switch',
        id: 'live-page-header-live',
        name: '隐藏 直播',
    },
    {
        type: 'switch',
        id: 'live-page-header-net-game',
        name: '隐藏 网游',
    },
    {
        type: 'switch',
        id: 'live-page-header-mobile-game',
        name: '隐藏 手游',
    },
    {
        type: 'switch',
        id: 'live-page-header-standalone-game',
        name: '隐藏 单机游戏',
    },
    {
        type: 'switch',
        id: 'live-page-header-standalone-vtuber',
        name: '隐藏 虚拟主播',
    },
    {
        type: 'switch',
        id: 'live-page-header-standalone-entertainment',
        name: '隐藏 娱乐',
    },
    {
        type: 'switch',
        id: 'live-page-header-standalone-radio',
        name: '隐藏 电台',
    },
    {
        type: 'switch',
        id: 'live-page-header-standalone-match',
        name: '隐藏 赛事',
    },
    {
        type: 'switch',
        id: 'live-page-header-standalone-chatroom',
        name: '隐藏 聊天室',
    },
    {
        type: 'switch',
        id: 'live-page-header-standalone-living',
        name: '隐藏 生活',
    },
    {
        type: 'switch',
        id: 'live-page-header-standalone-knowledge',
        name: '隐藏 知识',
    },
    {
        type: 'switch',
        id: 'live-page-header-standalone-helpmeplay',
        name: '隐藏 帮我玩',
    },
    {
        type: 'switch',
        id: 'live-page-header-standalone-interact',
        name: '隐藏 互动玩法',
    },
    {
        type: 'switch',
        id: 'live-page-header-standalone-shopping',
        name: '隐藏 购物',
    },
    {
        type: 'switch',
        id: 'live-page-header-showmore-link',
        name: '隐藏 更多',
        defaultEnable: true,
    },
]
