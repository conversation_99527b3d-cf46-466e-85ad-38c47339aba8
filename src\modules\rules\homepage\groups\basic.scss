// 隐藏 横幅banner
html[homepage-hide-banner] {
    .bili-header__banner {
        min-height: unset !important;
        height: 64px !important;
        background: var(--bg1, white) !important;
    }
    .bili-header__banner > * {
        display: none !important;
    }
    .bili-header__bar,
    .bili-feed4 .bili-header .slide-down {
        box-shadow: 0 2px 4px rgb(128 128 128 / 0.15) !important;
    }

    // icon和文字颜色
    .bili-header .right-entry__outside .right-entry-icon {
        color: var(--text1, #18191c) !important;
    }
    .bili-header .left-entry .entry-title,
    .bili-header .left-entry .download-entry,
    .bili-header .left-entry .default-entry,
    .bili-header .left-entry .loc-entry {
        color: var(--text1, #18191c) !important;
    }
    .bili-header .left-entry .entry-title .zhuzhan-icon {
        color: #00aeec !important;
    }
    .bili-header .right-entry__outside .right-entry-text {
        color: var(--text2, #61666d) !important;
    }

    // 动画
    .bili-feed4 .bili-header .slide-down {
        animation: headerSlideDown 0.3s linear forwards !important;
    }

    // 分区菜单 第一排按钮的二级菜单下置
    .v-popover.is-top {
        padding-top: 5px;
        padding-bottom: unset !important;
        bottom: unset !important;
    }
    @media (width >= 2200px) {
        .v-popover.is-top {
            top: 32px;
        }
    }
    @media (width >= 1701px) and (width <= 2199.9px) {
        .v-popover.is-top {
            top: 32px;
        }
    }
    @media (width >= 1367px) and (width <= 1700.9px) {
        .v-popover.is-top {
            top: 28px;
        }
    }
    @media (width >= 1100px) and (width <= 1366.9px) {
        .v-popover.is-top {
            top: 28px;
        }
    }
    @media (width <= 1099.9px) {
        .v-popover.is-top {
            top: 24px;
        }
    }
}

// 隐藏 大图活动轮播
html[homepage-hide-recommend-swipe] {
    .recommended-swipe {
        display: none !important;
    }

    .recommended-container_floor-aside .container {
        // 布局调整
        > *:nth-of-type(5),
        > *:nth-of-type(6),
        > *:nth-of-type(7),
        > *:nth-of-type(n + 8) {
            margin-top: 0 !important;
        }

        // 完全展示10个推荐项
        .feed-card:nth-of-type(n + 9),
        .feed-card:nth-of-type(n + 12) {
            display: inherit !important;
        }
        > *:nth-of-type(n + 13),
        .floor-single-card:first-of-type {
            margin-top: 0 !important;
        }
    }
}

// 隐藏 分区栏
html[homepage-hide-subarea] {
    #i_cecream .bili-header__channel .channel-icons {
        display: none !important;
    }
    #i_cecream .bili-header__channel .right-channel-container {
        display: none !important;
    }

    // adapt bilibili-app-recommend
    #i_cecream .bili-header__channel {
        height: 0 !important;
    }
    #i_cecream main.bili-feed4-layout:not(:has(.bilibili-app-recommend-root)) {
        margin-top: 20px !important;
    }
}

// 隐藏 滚动页面时 顶部吸附顶栏
html[homepage-hide-sticky-header] {
    .bili-header .left-entry__title svg {
        display: none !important;
    }

    // 高优先覆盖!important
    #i_cecream .bili-feed4 .bili-header .slide-down {
        box-shadow: unset !important;
    }
    #nav-searchform.is-actived::before,
    #nav-searchform.is-exper::before,
    #nav-searchform.is-exper:hover::before,
    #nav-searchform.is-focus::before,
    .bili-header .slide-down {
        background: unset !important;
    }
    .bili-header .slide-down {
        position: absolute !important;
        top: 0;
        animation: unset !important;
        box-shadow: unset !important;
    }
    .bili-header .slide-down .left-entry {
        margin-right: 30px !important;
    }
    .bili-header .slide-down .left-entry .default-entry,
    .bili-header .slide-down .left-entry .download-entry,
    .bili-header .slide-down .left-entry .entry-title,
    .bili-header .slide-down .left-entry .entry-title .zhuzhan-icon,
    .bili-header .slide-down .left-entry .loc-entry,
    .bili-header .slide-down .left-entry .loc-mc-box__text,
    .bili-header .slide-down .left-entry .mini-header__title,
    .bili-header .slide-down .right-entry .right-entry__outside .right-entry-icon,
    .bili-header .slide-down .right-entry .right-entry__outside .right-entry-text {
        color: #fff !important;
    }
    .bili-header .slide-down .download-entry,
    .bili-header .slide-down .loc-entry {
        display: unset !important;
    }
    .bili-header .slide-down .center-search-container,
    .bili-header .slide-down .center-search-container .center-search__bar {
        margin: 0 auto !important;
    }

    // 不可添加important, 否则与Evolved的黑暗模式冲突
    #nav-searchform {
        background: var(--bg3);
    }
    #nav-searchform:hover {
        background-color: var(--bg1) !important;
        opacity: 1;
    }
    #nav-searchform.is-focus {
        border: 1px solid var(--line_regular) !important;
        border-bottom: none !important;
        background: var(--bg1) !important;
    }
    #nav-searchform.is-actived.is-exper4-actived,
    #nav-searchform.is-focus.is-exper4-actived {
        border-bottom: unset !important;
    }

    // 只隐藏吸附header时的吸附分区栏
    #i_cecream .header-channel {
        top: 0 !important;
    }

    // Bilibili-Gate 顶栏
    .area-header-wrapper {
        top: 0 !important;
    }
}

// 隐藏 滚动页面时 顶部吸附分区栏
html[homepage-hide-sticky-subarea] {
    #i_cecream .header-channel {
        display: none !important;
    }

    // 动画
    .bili-feed4 .bili-header .slide-down {
        animation: headerSlideDown 0.3s linear forwards !important;
    }
    #i_cecream .bili-header__bar:not(.slide-down) {
        transition: background-color 0.2s linear;
    }
}

// 隐藏 顶部adblock提示
html[homepage-hide-adblock-tips] {
    .adblock-tips {
        display: none !important;
    }
}

// 恢复 原始动态按钮
html[homepage-revert-channel-dynamic-icon] {
    .bili-header__channel .channel-icons .icon-bg__dynamic {
        svg,
        picture {
            display: none !important;
        }
        &::after {
            content: '';
            width: 25px;
            height: 25px;
            background-image: url('data:image/svg+xml,<svg width="22" height="23" viewBox="0 0 22 23" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-bg--icon" data-v-674f5b07=""> <path d="M6.41659 15.625C3.88528 15.625 1.83325 13.7782 1.83325 11.5H10.9999C10.9999 13.7782 8.94789 15.625 6.41659 15.625Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M15.125 16.0827C15.125 18.614 13.2782 20.666 11 20.666L11 11.4993C13.2782 11.4993 15.125 13.5514 15.125 16.0827Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M6.875 6.91667C6.875 9.44797 8.72183 11.5 11 11.5L11 2.33333C8.72182 2.33333 6.875 4.38536 6.875 6.91667Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M15.5833 7.375C13.052 7.375 11 9.22183 11 11.5H20.1667C20.1667 9.22183 18.1146 7.375 15.5833 7.375Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
    }
}
