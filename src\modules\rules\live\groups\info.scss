// 隐藏 头像饰品
html[live-page-head-info-avatar-pendant] {
    .blive-avatar :is(.blive-avatar-pendant, .blive-avatar-icons) {
        display: none !important;
    }
}

// 隐藏 粉丝团
html[live-page-head-info-vm-upper-row-follow-ctnr] {
    #head-info-vm .upper-row .follow-ctnr {
        display: none !important;
    }
}

// 隐藏 xx人看过
html[live-page-head-info-vm-upper-row-visited] {
    #head-info-vm .upper-row .right-ctnr div:has(.watched-icon) {
        display: none !important;
    }
}

// 隐藏 人气
html[live-page-head-info-vm-upper-row-popular] {
    #head-info-vm .upper-row .right-ctnr div:has(.icon-popular),
    #LiveRoomHotrankEntries {
        display: none !important;
    }
}

// 隐藏 点赞
html[live-page-head-info-vm-upper-row-like] {
    #head-info-vm .upper-row .right-ctnr div:has(.like-icon) {
        display: none !important;
    }
}

// 隐藏 举报
html[live-page-head-info-vm-upper-row-report] {
    #head-info-vm .upper-row .right-ctnr div:has(.icon-report, [src*='img/report']) {
        display: none !important;
    }
}

// 隐藏 分享
html[live-page-head-info-vm-upper-row-share] {
    #head-info-vm .upper-row .right-ctnr div:has(.icon-share, [src*='img/share']) {
        display: none !important;
    }
    #head-info-vm .header-info-ctnr .rows-ctnr .upper-row .more {
        display: none !important;
    }
}

// 隐藏 人气榜
html[live-page-head-info-vm-lower-row-hot-rank] {
    #head-info-vm .lower-row .right-ctnr .popular-and-hot-rank {
        display: none !important;
    }
}

// 隐藏 礼物
html[live-page-head-info-vm-lower-row-gift-planet-entry] {
    #head-info-vm .lower-row .right-ctnr .gift-planet-entry {
        display: none !important;
    }
}

// 隐藏 活动
html[live-page-head-info-vm-lower-row-activity-gather-entry] {
    #head-info-vm .lower-row .right-ctnr .activity-gather-entry {
        display: none !important;
    }
}

// 隐藏 信息栏
html[live-page-head-info-vm] {
    #head-info-vm {
        display: none !important;
    }

    // 补齐圆角, 不可important
    #player-ctnr {
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
        overflow: hidden;
    }
}
