name: main-release

on:
    push:
        tags:
            - 'v*.*.*'

jobs:
    release:
        runs-on: ubuntu-latest
        permissions:
            contents: write
        steps:
            # environment
            - name: checkout code
              uses: actions/checkout@v4
              with:
                  ref: ${{ github.head_ref }}
            - name: install node and pnpm
              uses: pnpm/action-setup@v4
            - run: pnpm install

            # build and save
            - name: build and save file
              run: |
                pnpm run build
                rm -rf release && mkdir release
                cp dist/bilibili-cleaner.user.js release/

            # multi version, change metadata
            - name: jsdelivr, github, greasyfork version
              run: |
                cd release

                cp bilibili-cleaner.user.js bilibili-cleaner.jsdelivr.user.js
                sed -i '/^\/\/ @downloadURL/ c\// @downloadURL  https://cdn.jsdelivr.net/gh/festoney8/bilibili-cleaner@release/bilibili-cleaner.jsdelivr.user.js' bilibili-cleaner.jsdelivr.user.js
                sed -i '/^\/\/ @updateURL/ c\// @updateURL    https://cdn.jsdelivr.net/gh/festoney8/bilibili-cleaner@release/bilibili-cleaner.jsdelivr.user.js' bilibili-cleaner.jsdelivr.user.js

                cp bilibili-cleaner.user.js bilibili-cleaner.github.user.js
                sed -i '/^\/\/ @downloadURL/ c\// @downloadURL  https://raw.githubusercontent.com/festoney8/bilibili-cleaner/release/bilibili-cleaner.github.user.js' bilibili-cleaner.github.user.js
                sed -i '/^\/\/ @updateURL/ c\// @updateURL    https://raw.githubusercontent.com/festoney8/bilibili-cleaner/release/bilibili-cleaner.github.user.js' bilibili-cleaner.github.user.js

                mv bilibili-cleaner.user.js bilibili-cleaner.greasyfork.user.js
                cd ..

            # release
            - name: get version num
              id: get_vernum
              run: |
                  version=${{ github.ref_name }}
                  vernum=$( echo $version | sed 's/[^0-9]//g')
                  echo "New version: $vernum"
                  echo "VERSION_NUM=$vernum" >> "$GITHUB_OUTPUT"

            - name: release action
              uses: ncipollo/release-action@v1
              with:
                  artifacts: 'release/*.js'
                  allowUpdates: true
                  replacesArtifacts: true
                  generateReleaseNotes: true
                  body: '## [更新日志](https://github.com/festoney8/bilibili-cleaner/blob/main/CHANGELOG.md#${{ steps.get_vernum.outputs.VERSION_NUM }})'
                  token: ${{ secrets.GITHUB_TOKEN }}

            # commit to release branch
            - name: checkout
              uses: actions/checkout@v4
              with:
                ref: release
                path: branch-release

            - name: commit and push
              run: |
                cd branch-release
                rm -f *.user.js
                cp -R ../release/* ./
                ls -lah
                git config user.name "github-actions[bot]"
                git config user.email "41898282+github-actions[bot]@users.noreply.github.com"
                git add .
                git status
                git commit -m "CI: release ${{ github.ref_name }}"
                git push
