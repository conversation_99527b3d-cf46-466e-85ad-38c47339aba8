// Weibo Enhancer - Add blacklist functionality to Weibo hot posts
console.log('Weibo Enhancer loaded');

// Add custom CSS for better button styling
const style = document.createElement('style');
style.textContent = `
  .blacklist-btn {
    transition: all 0.2s ease !important;
  }

  .blacklist-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3) !important;
  }

  .blacklist-btn:active {
    transform: translateY(0) !important;
  }
`;
document.head.appendChild(style);

class WeiboEnhancer {
  constructor() {
    this.blacklistButtons = [];
    this.observer = null;

    this.init();
  }

  init() {
    // Wait for page to load
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.start());
    } else {
      this.start();
    }
  }

  start() {
    console.log('Starting Weibo Enhancer...');
    this.addBlacklistButtons();
    this.observeChanges();
  }

  addBlacklistButtons() {
    // Find all follow buttons in weibo posts
    const followButtons = document.querySelectorAll('.follow-btn_followbtn_FNC50');

    followButtons.forEach((followBtn) => {
      if (followBtn.parentElement && !followBtn.parentElement.querySelector('.blacklist-btn')) {
        this.createBlacklistButton(followBtn);
      }
    });
  }

  createBlacklistButton(followBtn) {
    // Find the usercard attribute from the avatar
    const article = followBtn.closest('article');
    if (!article) return;

    const avatar = article.querySelector('[usercard]');
    if (!avatar) return;

    const usercard = avatar.getAttribute('usercard');
    if (!usercard) return;

    // Create blacklist button with identical styling to follow button
    const blacklistBtn = document.createElement('button');
    blacklistBtn.className = 'woo-button-main woo-button-line woo-button-primary woo-button-s woo-button-round blacklist-btn';
    blacklistBtn.style.marginRight = '8px';
    blacklistBtn.style.backgroundColor = '#ff4757';
    blacklistBtn.style.borderColor = '#ff4757';
    blacklistBtn.style.color = '#fff';
    blacklistBtn.innerHTML = `
      <span class="woo-button-wrap">
        <span class="woo-button-content">
          <i class="woo-font woo-font--close"></i> 拉黑
        </span>
      </span>
    `;

    // Add hover effects
    blacklistBtn.addEventListener('mouseenter', () => {
      blacklistBtn.style.backgroundColor = '#ff3742';
      blacklistBtn.style.borderColor = '#ff3742';
    });

    blacklistBtn.addEventListener('mouseleave', () => {
      blacklistBtn.style.backgroundColor = '#ff4757';
      blacklistBtn.style.borderColor = '#ff4757';
    });

    // Add click event with proper event handling
    blacklistBtn.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
      this.handleBlacklist(usercard, article);
    });

    // Also prevent avatar link navigation during automation
    const avatarLink = article.querySelector('a[href*="/u/"]');
    if (avatarLink) {
      avatarLink.addEventListener('click', (e) => {
        // Only prevent if we're in the middle of blacklist process
        if (blacklistBtn.textContent?.includes('处理中')) {
          e.preventDefault();
          e.stopPropagation();
          console.log('Prevented avatar navigation during blacklist process');
        }
      });
    }

    // Insert before follow button (to the left)
    followBtn.parentElement.insertBefore(blacklistBtn, followBtn);

    this.blacklistButtons.push({
      element: blacklistBtn,
      usercard: usercard
    });
  }

  async handleBlacklist(usercard, article) {
    console.log(`Starting blacklist process for user: ${usercard}`);

    // Show loading state on button
    const blacklistBtn = article.querySelector('.blacklist-btn');
    const originalText = blacklistBtn?.innerHTML;
    if (blacklistBtn) {
      blacklistBtn.innerHTML = `
        <span class="woo-button-wrap">
          <span class="woo-button-content">处理中...</span>
        </span>
      `;
      blacklistBtn.style.pointerEvents = 'none';
    }

    try {
      console.log('Step 1: Hovering over avatar...');
      await this.hoverAvatar(article);

      console.log('Step 2: Clicking menu button...');
      await this.clickMenuButton();

      console.log('Step 3: Clicking blacklist option...');
      await this.clickBlacklistOption();

      console.log('Step 4: Confirming blacklist...');
      await this.confirmBlacklist();

      console.log(`Successfully blacklisted user: ${usercard}`);

      // Hide the post
      this.hidePost(article);

    } catch (error) {
      console.error('Blacklist process failed:', error);

      // Restore button state
      if (blacklistBtn && originalText) {
        blacklistBtn.innerHTML = originalText;
        blacklistBtn.style.pointerEvents = 'auto';
      }

      // Show detailed error message
      const errorMsg = error && error.message ? error.message : 'Unknown error';
      alert(`拉黑失败: ${errorMsg}\n请手动操作或检查控制台日志`);
    }
  }

  async hoverAvatar(article) {
    return new Promise((resolve, reject) => {
      // Step 1: Find the avatar element (from step1.html structure)
      const avatar = article.querySelector('.woo-avatar-main.woo-avatar-hover, .head_avatar_20c9y');
      if (!avatar) {
        console.error('Avatar not found in article:', article);
        reject(new Error('Avatar not found'));
        return;
      }

      console.log('Found avatar, triggering hover events (no click)...');

      // Only trigger hover events, do NOT click to prevent navigation
      const hoverEvent = new MouseEvent('mouseenter', {
        bubbles: true,
        cancelable: true,
        view: window
      });

      avatar.dispatchEvent(hoverEvent);

      // Also trigger mouseover for better compatibility
      const mouseOverEvent = new MouseEvent('mouseover', {
        bubbles: true,
        cancelable: true,
        view: window
      });

      avatar.dispatchEvent(mouseOverEvent);

      // Wait for user card popup to appear
      let attempts = 0;
      const maxAttempts = 5;

      const checkForUserCard = () => {
        attempts++;
        // Look for the popup card that should appear on hover
        const userCard = document.querySelector('.PopCard_pop_1ghGz, .woo-pop-wrap, [class*="PopCard"]');

        if (userCard) {
          console.log('User card popup appeared:', userCard);
          resolve();
        } else if (attempts < maxAttempts) {
          console.log(`User card not found, attempt ${attempts}/${maxAttempts}`);
          // Try triggering hover again
          avatar.dispatchEvent(hoverEvent);
          setTimeout(checkForUserCard, 600);
        } else {
          console.error('User card did not appear after', maxAttempts, 'attempts');
          reject(new Error('User card did not appear'));
        }
      };

      setTimeout(checkForUserCard, 800);
    });
  }

  async clickMenuButton() {
    return new Promise((resolve, reject) => {
      // Step 2: Look for the ellipsis menu button from step2.html structure
      const menuSelectors = [
        '.PopCard_ellipsis_1tfem',  // From step2.html
        '.woo-pop-wrap .woo-pop-ctrl',  // From step2.html
        '.woo-font--ellipsis',  // The ellipsis icon
        '.PopCard_pop_1ghGz .woo-pop-ctrl'  // Full path from step2.html
      ];

      let menuButton = null;

      for (const selector of menuSelectors) {
        menuButton = document.querySelector(selector);
        if (menuButton) {
          console.log('Found menu button with selector:', selector);
          break;
        }
      }

      if (!menuButton) {
        console.error('Menu button not found. Available popup elements:',
          Array.from(document.querySelectorAll('[class*="PopCard"], [class*="pop"], .woo-font--ellipsis')));
        reject(new Error('Menu button not found'));
        return;
      }

      console.log('Clicking menu button:', menuButton);
      menuButton.click();

      // Wait for dropdown menu to appear
      let attempts = 0;
      const maxAttempts = 3;

      const checkForMenu = () => {
        attempts++;
        // Look for the dropdown menu that should appear
        const menu = document.querySelector('.woo-pop-content, [role="button"]:contains("加入黑名单"), .woo-pop-item-main');

        if (menu) {
          console.log('Dropdown menu appeared:', menu);
          resolve();
        } else if (attempts < maxAttempts) {
          console.log(`Menu not found, attempt ${attempts}/${maxAttempts}`);
          setTimeout(checkForMenu, 400);
        } else {
          console.error('Menu did not appear after', maxAttempts, 'attempts');
          reject(new Error('Menu did not appear'));
        }
      };

      setTimeout(checkForMenu, 400);
    });
  }

  async clickBlacklistOption() {
    return new Promise((resolve, reject) => {
      // Step 3: Look for "加入黑名单" option from step3.html structure
      const blacklistSelectors = [
        '.woo-pop-item-main[role="button"]',  // From step3.html
        '[role="button"]:contains("加入黑名单")',
        '.woo-box-flex.woo-box-alignCenter.woo-pop-item-main'  // Full class from step3.html
      ];

      let blacklistOption = null;

      // First try the exact selectors from step3.html
      for (const selector of blacklistSelectors) {
        blacklistOption = document.querySelector(selector);
        if (blacklistOption && blacklistOption.textContent?.includes('加入黑名单')) {
          console.log('Found blacklist option with selector:', selector);
          break;
        }
      }

      // Fallback: search through all elements with role="button"
      if (!blacklistOption) {
        const allButtons = Array.from(document.querySelectorAll('[role="button"], .woo-pop-item-main'));
        blacklistOption = allButtons.find(item => {
          const text = item.textContent?.trim() || '';
          return text.includes('加入黑名单') ||
                 text.includes('黑名单') ||
                 text.includes('拉黑') ||
                 text.includes('屏蔽');
        });
      }

      if (!blacklistOption) {
        // Log all available menu items for debugging
        const allMenuItems = Array.from(document.querySelectorAll('[role="button"], .woo-pop-item-main, .woo-pop-content *'));
        console.error('Blacklist option not found. Available menu items:',
          allMenuItems.map(item => item.textContent?.trim()).filter(text => text));
        reject(new Error('Blacklist option not found'));
        return;
      }

      console.log('Clicking blacklist option:', blacklistOption.textContent);
      blacklistOption.click();

      // Wait for confirmation dialog
      let attempts = 0;
      const maxAttempts = 3;

      const checkForDialog = () => {
        attempts++;
        // Look for dialog with the confirm button from step4.html
        const confirmDialog = document.querySelector('.woo-dialog-btn, .woo-button-main.woo-button-flat, [class*="dialog"]');

        if (confirmDialog) {
          console.log('Confirmation dialog appeared:', confirmDialog);
          resolve();
        } else if (attempts < maxAttempts) {
          console.log(`Dialog not found, attempt ${attempts}/${maxAttempts}`);
          setTimeout(checkForDialog, 400);
        } else {
          console.error('Confirmation dialog did not appear after', maxAttempts, 'attempts');
          reject(new Error('Confirmation dialog did not appear'));
        }
      };

      setTimeout(checkForDialog, 400);
    });
  }

  async confirmBlacklist() {
    return new Promise((resolve, reject) => {
      // Step 4: Look for confirm button from step4.html structure
      const confirmSelectors = [
        '.woo-button-main.woo-button-flat.woo-button-primary.woo-button-m.woo-button-round.woo-dialog-btn',  // Exact from step4.html
        '.woo-dialog-btn',  // Shorter version
        '.woo-button-flat.woo-button-primary',  // Key classes
        'button.woo-dialog-btn'  // Button with dialog class
      ];

      let confirmButton = null;

      // First try the exact selectors from step4.html
      for (const selector of confirmSelectors) {
        confirmButton = document.querySelector(selector);
        if (confirmButton && confirmButton.textContent?.trim().includes('确定')) {
          console.log('Found confirm button with selector:', selector);
          break;
        }
      }

      // Fallback: search through all buttons for "确定"
      if (!confirmButton) {
        const allButtons = Array.from(document.querySelectorAll('button, .woo-button-main'));
        confirmButton = allButtons.find(btn => {
          const text = btn.textContent?.trim() || '';
          return text.includes('确定') ||
                 text.includes('确认') ||
                 text.includes('OK') ||
                 text === '确';
        });
      }

      if (!confirmButton) {
        // Log all available buttons for debugging
        const allButtons = Array.from(document.querySelectorAll('button, .woo-button-main, [class*="dialog"] button'));
        console.error('Confirm button not found. Available buttons:',
          allButtons.map(btn => btn.textContent?.trim()).filter(text => text));
        reject(new Error('Confirm button not found'));
        return;
      }

      console.log('Clicking confirm button:', confirmButton.textContent);
      confirmButton.click();

      // Wait for action to complete
      setTimeout(() => {
        console.log('Blacklist action completed successfully');
        resolve();
      }, 1000);
    });
  }

  hidePost(article) {
    article.style.opacity = '0.3';
    article.style.pointerEvents = 'none';

    // Add a notice
    const notice = document.createElement('div');
    notice.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px 20px;
      border-radius: 4px;
      z-index: 1000;
      font-size: 14px;
    `;
    notice.textContent = '已拉黑该用户';

    article.style.position = 'relative';
    article.appendChild(notice);
  }

  observeChanges() {
    this.observer = new MutationObserver((mutations) => {
      let shouldUpdate = false;

      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          shouldUpdate = true;
        }
      });

      if (shouldUpdate) {
        // Debounce the update
        setTimeout(() => {
          this.addBlacklistButtons();
        }, 500);
      }
    });

    this.observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  destroy() {
    if (this.observer) {
      this.observer.disconnect();
    }

    this.blacklistButtons.forEach(btn => {
      btn.element.remove();
    });

    this.blacklistButtons = [];
  }
}

// Initialize the enhancer
const weiboEnhancer = new WeiboEnhancer();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  weiboEnhancer.destroy();
});
