/**
 * Weibo Enhancer - Add blacklist functionality to Weibo hot posts
 * 模块化重构版本
 */

import { injectStyles } from './styles.js';
import { ButtonManager } from './button-manager.js';

console.log('Weibo Enhancer loaded');

class WeiboEnhancer {
  constructor() {
    this.buttonManager = new ButtonManager();
    this.observer = null;
    this.init();
  }

  /**
   * 初始化增强器
   */
  init() {
    // 注入自定义样式
    injectStyles();

    // 等待页面加载
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.start());
    } else {
      this.start();
    }
  }

  /**
   * 启动增强器
   */
  start() {
    console.log('Starting Weibo Enhancer...');
    this.buttonManager.addBlacklistButtons();
    this.observeChanges();
  }

  /**
   * 监听DOM变化，自动添加拉黑按钮
   */
  observeChanges() {
    this.observer = new MutationObserver((mutations) => {
      let shouldUpdate = false;

      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          shouldUpdate = true;
        }
      });

      if (shouldUpdate) {
        // 防抖更新
        setTimeout(() => {
          this.buttonManager.addBlacklistButtons();
        }, 500);
      }
    });

    this.observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  /**
   * 销毁增强器，清理资源
   */
  destroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
    this.buttonManager.destroy();
  }
}

// 初始化增强器
const weiboEnhancer = new WeiboEnhancer();

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
  weiboEnhancer.destroy();
});
