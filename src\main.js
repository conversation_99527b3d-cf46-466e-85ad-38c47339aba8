/**
 * Weibo Enhancer - Custom blacklist system for Weibo hot posts
 * 自定义黑名单系统版本
 */

import { BlacklistStorage } from './blacklist-storage.js';
import { UserExtractor } from './user-extractor.js';
import { ContextMenu } from './context-menu.js';
import { PostHider } from './post-hider.js';

console.log('Weibo Enhancer loaded - Custom Blacklist System');

class WeiboEnhancer {
  constructor() {
    this.blacklistStorage = null;
    this.userExtractor = null;
    this.contextMenu = null;
    this.postHider = null;

    this.init();
  }

  /**
   * 初始化增强器
   */
  init() {
    // 等待页面加载
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.start());
    } else {
      this.start();
    }
  }

  /**
   * 启动增强器
   */
  start() {
    console.log('Starting Weibo Enhancer with custom blacklist system...');

    try {
      // 初始化核心模块
      this.blacklistStorage = new BlacklistStorage();
      this.userExtractor = new UserExtractor();

      // 初始化功能模块
      this.contextMenu = new ContextMenu(this.blacklistStorage, this.userExtractor);
      this.postHider = new PostHider(this.blacklistStorage, this.userExtractor);

      // 显示启动信息
      this.showStartupInfo();

      console.log('Weibo Enhancer started successfully');
    } catch (error) {
      console.error('Failed to start Weibo Enhancer:', error);
    }
  }

  /**
   * 显示启动信息
   */
  showStartupInfo() {
    const stats = this.blacklistStorage.getStats();
    const postStats = this.postHider.getStats();

    console.log('=== Weibo Enhancer Status ===');
    console.log(`Blacklisted users: ${stats.total}`);
    console.log(`Hidden posts: ${postStats.hiddenPostsCount}`);
    console.log('Right-click on any weibo to access blacklist options');
    console.log('=============================');

    // 显示简短的用户提示
    if (stats.total === 0) {
      setTimeout(() => {
        this.showNotification('微博增强器已启动！右键点击微博可屏蔽用户', 'info');
      }, 2000);
    } else {
      setTimeout(() => {
        this.showNotification(`已屏蔽 ${stats.total} 个用户，隐藏 ${postStats.hiddenPostsCount} 条微博`, 'success');
      }, 2000);
    }
  }

  /**
   * 显示通知
   * @param {string} message - 通知消息
   * @param {string} type - 通知类型
   */
  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'success' ? '#52c41a' : type === 'error' ? '#ff4d4f' : '#1890ff'};
      color: white;
      padding: 12px 20px;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      z-index: 10001;
      font-size: 14px;
      max-width: 300px;
      word-wrap: break-word;
    `;

    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 4000);
  }

  /**
   * 获取增强器状态
   * @returns {Object} 状态信息
   */
  getStatus() {
    if (!this.blacklistStorage || !this.postHider) {
      return { initialized: false };
    }

    return {
      initialized: true,
      blacklistStats: this.blacklistStorage.getStats(),
      postStats: this.postHider.getStats(),
      modules: {
        blacklistStorage: !!this.blacklistStorage,
        userExtractor: !!this.userExtractor,
        contextMenu: !!this.contextMenu,
        postHider: !!this.postHider
      }
    };
  }

  /**
   * 销毁增强器，清理资源
   */
  destroy() {
    console.log('Destroying Weibo Enhancer...');

    if (this.contextMenu) {
      this.contextMenu.destroy();
    }

    if (this.postHider) {
      this.postHider.destroy();
    }

    console.log('Weibo Enhancer destroyed');
  }
}

// 初始化增强器
const weiboEnhancer = new WeiboEnhancer();

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
  weiboEnhancer.destroy();
});
