import { Item } from '@/types/item'

export const commonHeaderLeftItems: Item[] = [
    {
        type: 'switch',
        id: 'common-hide-nav-homepage-logo',
        name: '隐藏 主站Logo',
    },
    {
        type: 'switch',
        id: 'common-hide-nav-homepage',
        name: '隐藏 首页',
    },
    {
        type: 'switch',
        id: 'common-hide-nav-anime',
        name: '隐藏 番剧',
    },
    {
        type: 'switch',
        id: 'common-hide-nav-live',
        name: '隐藏 直播',
    },
    {
        type: 'switch',
        id: 'common-hide-nav-game',
        name: '隐藏 游戏中心',
    },
    {
        type: 'switch',
        id: 'common-hide-nav-vipshop',
        name: '隐藏 会员购',
    },
    {
        type: 'switch',
        id: 'common-hide-nav-manga',
        name: '隐藏 漫画',
    },
    {
        type: 'switch',
        id: 'common-hide-nav-match',
        name: '隐藏 赛事',
    },
    {
        type: 'switch',
        id: 'common-hide-nav-bdu',
        name: '隐藏 百大评选',
    },
    {
        type: 'switch',
        id: 'common-hide-nav-bml',
        name: '隐藏 BML',
    },
    {
        type: 'switch',
        id: 'common-hide-nav-download-app',
        name: '隐藏 下载客户端',
        defaultEnable: true,
    },
    {
        type: 'switch',
        id: 'common-hide-nav-blackboard',
        name: '隐藏 所有官方活动/活动直播',
    },
    {
        type: 'switch',
        id: 'common-hide-nav-channel-panel-popover',
        name: '隐藏 首页弹出框',
    },
    {
        type: 'switch',
        id: 'common-hide-nav-anime-popover',
        name: '隐藏 番剧弹出框',
    },
    {
        type: 'switch',
        id: 'common-hide-nav-live-popover',
        name: '隐藏 直播弹出框',
    },
    {
        type: 'switch',
        id: 'common-hide-nav-game-popover',
        name: '隐藏 游戏中心弹出框',
    },
    {
        type: 'switch',
        id: 'common-hide-nav-manga-popover',
        name: '隐藏 漫画弹出框',
    },
]
