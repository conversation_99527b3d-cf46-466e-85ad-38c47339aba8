/**
 * 拉黑操作模块
 * 处理拉黑用户的具体步骤
 */

import { SELECTORS, BLACKLIST_KEYWORDS, CONFIRM_KEYWORDS } from './selectors.js';
import { findElementBySelectors, findElementByText, waitForElement, triggerHover } from './dom-utils.js';

/**
 * 悬停头像显示用户卡片
 * @param {Element} article - 文章元素
 * @returns {Promise<void>}
 */
export async function hoverAvatar(article) {
  return new Promise((resolve, reject) => {
    const avatar = article.querySelector(SELECTORS.AVATAR_ELEMENTS);
    if (!avatar) {
      console.error('Avatar not found in article:', article);
      reject(new Error('Avatar not found'));
      return;
    }

    console.log('Found avatar, triggering hover events...');
    triggerHover(avatar);

    // 等待用户卡片弹出
    let attempts = 0;
    const maxAttempts = 5;

    const checkForUserCard = () => {
      attempts++;
      const userCard = document.querySelector(SELECTORS.USER_CARD_POPUP);

      if (userCard) {
        console.log('User card popup appeared:', userCard);
        resolve();
      } else if (attempts < maxAttempts) {
        console.log(`User card not found, attempt ${attempts}/${maxAttempts}`);
        triggerHover(avatar); // 重新触发悬停
        setTimeout(checkForUserCard, 600);
      } else {
        console.error('User card did not appear after', maxAttempts, 'attempts');
        reject(new Error('User card did not appear'));
      }
    };

    setTimeout(checkForUserCard, 800);
  });
}

/**
 * 悬停菜单按钮以显示下拉菜单
 * @returns {Promise<void>}
 */
export async function clickMenuButton() {
  return new Promise((resolve, reject) => {
    const menuButton = findElementBySelectors(SELECTORS.MENU_BUTTONS);

    if (!menuButton) {
      console.error('Menu button not found. Available popup elements:',
        Array.from(document.querySelectorAll('[class*="PopCard"], [class*="pop"], .woo-font--ellipsis')));
      reject(new Error('Menu button not found'));
      return;
    }

    console.log('Hovering over menu button (not clicking):', menuButton);
    
    // 触发悬停事件而不是点击事件
    triggerHover(menuButton);

    // 等待下拉菜单出现 - 使用更智能的检测
    let attempts = 0;
    const maxAttempts = 5;
    
    const checkForMenu = () => {
      attempts++;
      
      // 尝试多个可能的菜单选择器
      const menuSelectors = [
        '.woo-pop-content',
        '.woo-pop-item-main',
        '[role="button"]',
        '.woo-pop-wrap [role="button"]',
        '[class*="PopCard"] [role="button"]',
        '.woo-pop-item',
        '[class*="pop"] [role="button"]',
        '.popcard [role="button"]'
      ];
      
      let menuFound = false;
      
      for (const selector of menuSelectors) {
        try {
          const elements = document.querySelectorAll(selector);
          if (elements.length > 0) {
            // 检查是否有包含拉黑相关文本的元素
            const hasBlacklistOption = Array.from(elements).some(el => {
              const text = el.textContent?.trim() || '';
              return BLACKLIST_KEYWORDS.some(keyword => text.includes(keyword));
            });
            
            if (hasBlacklistOption || elements.length > 0) {
              console.log('Dropdown menu appeared with selector:', selector, 'Elements found:', elements.length);
              menuFound = true;
              break;
            }
          }
        } catch (e) {
          // 忽略无效选择器
        }
      }
      
      if (menuFound) {
        console.log('Menu found, waiting a bit more for content to load...');
        // 再等待一点时间确保菜单内容完全加载
        setTimeout(() => resolve(), 300);
      } else if (attempts < maxAttempts) {
        console.log(`Menu not found, attempt ${attempts}/${maxAttempts}`);
        // 再次触发悬停
        triggerHover(menuButton);
        setTimeout(checkForMenu, 500);
      } else {
        // 记录所有可能的菜单元素用于调试
        const allPossibleMenus = Array.from(document.querySelectorAll('*')).filter(el => {
          const text = el.textContent?.trim() || '';
          const className = el.className || '';
          return text.length > 0 && text.length < 50 && // 合理的文本长度
                 (className.includes('pop') || className.includes('menu') || 
                  className.includes('dropdown') || el.getAttribute('role') === 'button');
        });
        
        console.error('Menu did not appear after', maxAttempts, 'attempts. Available elements:', 
          allPossibleMenus.slice(0, 20).map(el => ({ // 限制输出数量
            tagName: el.tagName,
            className: el.className,
            textContent: el.textContent?.trim(),
            role: el.getAttribute('role')
          })));
        reject(new Error('Menu did not appear'));
      }
    };
    
    setTimeout(checkForMenu, 600); // 初始等待时间
  });
}

/**
 * 点击拉黑选项
 * @returns {Promise<void>}
 */
export async function clickBlacklistOption() {
  return new Promise((resolve, reject) => {
    console.log('Looking for blacklist option...');
    
    // 等待一下确保菜单完全加载
    setTimeout(() => {
      // 基于step3.html的精确选择器
      const exactSelector = '.woo-box-flex.woo-box-alignCenter.woo-pop-item-main[role="button"]';
      let blacklistOption = document.querySelector(exactSelector);
      
      if (blacklistOption && blacklistOption.textContent?.includes('加入黑名单')) {
        console.log('Found blacklist option with exact selector:', blacklistOption);
      } else {
        // 尝试更通用的方法
        blacklistOption = findElementByText(SELECTORS.BLACKLIST_OPTIONS, BLACKLIST_KEYWORDS);
        
        if (!blacklistOption) {
          console.log('Trying expanded search for blacklist option...');
          
          // 查找所有包含"加入黑名单"文本的元素
          const allElements = Array.from(document.querySelectorAll('*'));
          blacklistOption = allElements.find(element => {
            const text = element.textContent?.trim() || '';
            return text === '加入黑名单' || BLACKLIST_KEYWORDS.some(keyword => text.includes(keyword));
          });
          
          if (blacklistOption) {
            console.log('Found blacklist option with text search:', blacklistOption);
          }
        }
      }
      
      if (!blacklistOption) {
        // 简化的调试信息
        const allVisible = Array.from(document.querySelectorAll('*')).filter(el => {
          const style = window.getComputedStyle(el);
          const text = el.textContent?.trim() || '';
          return style.display !== 'none' && 
                 style.visibility !== 'hidden' && 
                 text.length > 0 && 
                 text.length < 50;
        });
        
        console.error('Blacklist option not found. Visible elements:');
        allVisible.slice(0, 10).forEach((el, index) => {
          console.error(`${index + 1}. ${el.tagName}.${el.className} = "${el.textContent?.trim()}"`);
        });
        
        reject(new Error('Blacklist option not found'));
        return;
      }
      
      // 找到了拉黑选项，点击它
      console.log('Clicking blacklist option:', blacklistOption.textContent);
      blacklistOption.click();
      
      // 等待确认对话框出现
      setTimeout(() => {
        // 尝试多种对话框选择器
        const dialogSelectors = [
          '.woo-dialog-main',
          '.woo-modal-main',
          '[role="alertdialog"]',
          '[class*="dialog"]',
          '[class*="modal"]',
          '.woo-dialog-btn'
        ];

        let dialog = null;
        for (const selector of dialogSelectors) {
          dialog = document.querySelector(selector);
          if (dialog) {
            console.log('Confirmation dialog found with selector:', selector, dialog);
            break;
          }
        }

        if (dialog) {
          console.log('Confirmation dialog appeared');
          resolve();
        } else {
          // 记录当前页面上所有可能的对话框元素
          console.error('Confirmation dialog did not appear. Searching for any dialog-like elements...');

          const allPossibleDialogs = Array.from(document.querySelectorAll('*')).filter(el => {
            const className = el.className || '';
            const text = el.textContent?.trim() || '';
            return (className.includes('dialog') ||
                   className.includes('modal') ||
                   className.includes('confirm') ||
                   text.includes('确定') ||
                   text.includes('取消') ||
                   text.includes('屏蔽')) &&
                   text.length < 200; // 避免太长的文本
          });

          console.error('Found possible dialog elements:');
          allPossibleDialogs.slice(0, 10).forEach((el, index) => {
            console.error(`${index + 1}. ${el.tagName}.${el.className} = "${el.textContent?.trim().substring(0, 100)}"`);
          });

          reject(new Error('Confirmation dialog did not appear'));
        }
      }, 800);
      
    }, 500); // 等待500ms确保菜单加载完成
  });
}

/**
 * 确认拉黑操作
 * @returns {Promise<void>}
 */
export async function confirmBlacklist() {
  return new Promise((resolve, reject) => {
    console.log('Starting confirmBlacklist function...');

    // 首先检查页面上是否有任何对话框或按钮
    console.log('Checking for any dialog or button elements on the page...');

    const allButtons = Array.from(document.querySelectorAll('button, [role="button"], .woo-button-main, [class*="button"], [class*="btn"]'));
    console.log('Found buttons on page:', allButtons.length);

    const allDialogs = Array.from(document.querySelectorAll('[class*="dialog"], [class*="modal"], [role="dialog"], [role="alertdialog"]'));
    console.log('Found dialog elements on page:', allDialogs.length);

    // 等待一下确保对话框完全加载
    setTimeout(() => {
      // 首先尝试精确的选择器 - 基于comfirm-modal.html
      const exactSelector = '.woo-button-main.woo-button-flat.woo-button-primary.woo-button-m.woo-button-round.woo-dialog-btn';
      let confirmButton = document.querySelector(exactSelector);

      // 检查是否包含"确定"文本
      if (confirmButton && confirmButton.textContent?.trim().includes('确定')) {
        console.log('Found confirm button with exact selector:', confirmButton);
      } else {
        // 尝试通过文本内容查找
        confirmButton = findElementByText(SELECTORS.CONFIRM_BUTTONS, CONFIRM_KEYWORDS);

        if (!confirmButton) {
          // 尝试查找所有按钮，然后过滤出"确定"按钮
          const allButtons = Array.from(document.querySelectorAll('button, .woo-button-main, [class*="dialog"] button'));
          confirmButton = allButtons.find(btn => {
            const text = btn.textContent?.trim() || '';
            return CONFIRM_KEYWORDS.some(keyword => text.includes(keyword));
          });
        }
      }

      if (!confirmButton) {
        // 记录所有可用按钮用于调试
        const allButtons = Array.from(document.querySelectorAll('button, .woo-button-main, [class*="dialog"] button, .woo-dialog-btn, [class*="btn"]'));
        console.error('Confirm button not found. Available buttons:', allButtons.length);

        if (allButtons.length > 0) {
          allButtons.forEach((btn, index) => {
            console.error(`${index + 1}. ${btn.tagName}.${btn.className} = "${btn.textContent?.trim()}"`);
          });
        } else {
          console.error('No buttons found on the page!');

          // 尝试直接查找包含"确定"文本的任何元素
          const allElements = Array.from(document.querySelectorAll('*'));
          const confirmElements = allElements.filter(el => {
            const text = el.textContent?.trim() || '';
            return CONFIRM_KEYWORDS.some(keyword => text.includes(keyword));
          });

          console.error('Elements containing confirm keywords:', confirmElements.length);
          confirmElements.forEach((el, index) => {
            console.error(`${index + 1}. ${el.tagName}.${el.className} = "${el.textContent?.trim()}"`);
          });

          // 尝试点击第一个找到的元素
          if (confirmElements.length > 0) {
            console.log('Attempting to click first element with confirm text:', confirmElements[0]);
            confirmElements[0].click();
            setTimeout(() => resolve(), 1000);
            return;
          }
        }

        reject(new Error('Confirm button not found'));
        return;
      }

      console.log('Clicking confirm button:', confirmButton.textContent);
      confirmButton.click();

      // 等待操作完成
      setTimeout(() => {
        console.log('Blacklist action completed successfully');
        resolve();
      }, 1000);

    }, 300); // 等待对话框加载
  });
}
