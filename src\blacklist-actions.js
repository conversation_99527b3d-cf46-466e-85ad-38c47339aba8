/**
 * 拉黑操作模块
 * 处理拉黑用户的具体步骤
 */

import { SELECTORS, BLACKLIST_KEYWORDS, CONFIRM_KEYWORDS } from './selectors.js';
import { findElementBySelectors, findElementByText, waitForElement, triggerHover } from './dom-utils.js';

/**
 * 悬停头像显示用户卡片
 * @param {Element} article - 文章元素
 * @returns {Promise<void>}
 */
export async function hoverAvatar(article) {
  return new Promise((resolve, reject) => {
    const avatar = article.querySelector(SELECTORS.AVATAR_ELEMENTS);
    if (!avatar) {
      console.error('Avatar not found in article:', article);
      reject(new Error('Avatar not found'));
      return;
    }

    console.log('Found avatar, triggering hover events...');
    triggerHover(avatar);

    // 等待用户卡片弹出
    let attempts = 0;
    const maxAttempts = 5;

    const checkForUserCard = () => {
      attempts++;
      const userCard = document.querySelector(SELECTORS.USER_CARD_POPUP);

      if (userCard) {
        console.log('User card popup appeared:', userCard);
        resolve();
      } else if (attempts < maxAttempts) {
        console.log(`User card not found, attempt ${attempts}/${maxAttempts}`);
        triggerHover(avatar); // 重新触发悬停
        setTimeout(checkForUserCard, 600);
      } else {
        console.error('User card did not appear after', maxAttempts, 'attempts');
        reject(new Error('User card did not appear'));
      }
    };

    setTimeout(checkForUserCard, 800);
  });
}

/**
 * 点击菜单按钮
 * @returns {Promise<void>}
 */
export async function clickMenuButton() {
  return new Promise((resolve, reject) => {
    const menuButton = findElementBySelectors(SELECTORS.MENU_BUTTONS);

    if (!menuButton) {
      console.error('Menu button not found. Available popup elements:',
        Array.from(document.querySelectorAll('[class*="PopCard"], [class*="pop"], .woo-font--ellipsis')));
      reject(new Error('Menu button not found'));
      return;
    }

    console.log('Clicking menu button:', menuButton);

    // 尝试多种点击方式以确保菜单打开
    menuButton.click();

    // 也尝试触发鼠标事件
    const clickEvent = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window
    });
    menuButton.dispatchEvent(clickEvent);

    // 等待下拉菜单出现 - 使用更智能的检测
    let attempts = 0;
    const maxAttempts = 5;

    const checkForMenu = () => {
      attempts++;

      // 尝试多个可能的菜单选择器
      const menuSelectors = [
        '.woo-pop-content',
        '.woo-pop-item-main',
        '[role="button"]',
        '.woo-pop-wrap [role="button"]',
        '[class*="PopCard"] [role="button"]',
        '.woo-pop-item',
        '[class*="pop"] [role="button"]',
        '.popcard [role="button"]'
      ];

      let menuFound = false;

      for (const selector of menuSelectors) {
        try {
          const elements = document.querySelectorAll(selector);
          if (elements.length > 0) {
            // 检查是否有包含拉黑相关文本的元素
            const hasBlacklistOption = Array.from(elements).some(el => {
              const text = el.textContent?.trim() || '';
              return BLACKLIST_KEYWORDS.some(keyword => text.includes(keyword));
            });

            if (hasBlacklistOption || elements.length > 0) {
              console.log('Dropdown menu appeared with selector:', selector, 'Elements found:', elements.length);
              menuFound = true;
              break;
            }
          }
        } catch (e) {
          // 忽略无效选择器
        }
      }

      if (menuFound) {
        console.log('Menu found, waiting a bit more for content to load...');
        // 再等待一点时间确保菜单内容完全加载
        setTimeout(() => resolve(), 300);
      } else if (attempts < maxAttempts) {
        console.log(`Menu not found, attempt ${attempts}/${maxAttempts}`);
        setTimeout(checkForMenu, 500);
      } else {
        // 记录所有可能的菜单元素用于调试
        const allPossibleMenus = Array.from(document.querySelectorAll('*')).filter(el => {
          const text = el.textContent?.trim() || '';
          const className = el.className || '';
          return text.length > 0 && text.length < 50 && // 合理的文本长度
                 (className.includes('pop') || className.includes('menu') ||
                  className.includes('dropdown') || el.getAttribute('role') === 'button');
        });

        console.error('Menu did not appear after', maxAttempts, 'attempts. Available elements:',
          allPossibleMenus.slice(0, 20).map(el => ({ // 限制输出数量
            tagName: el.tagName,
            className: el.className,
            textContent: el.textContent?.trim(),
            role: el.getAttribute('role')
          })));
        reject(new Error('Menu did not appear'));
      }
    };

    setTimeout(checkForMenu, 600); // 初始等待时间
  });
}

/**
 * 点击拉黑选项
 * @returns {Promise<void>}
 */
export async function clickBlacklistOption() {
  return new Promise((resolve, reject) => {
    // 首先尝试原有的方法
    let blacklistOption = findElementByText(SELECTORS.BLACKLIST_OPTIONS, BLACKLIST_KEYWORDS);

    if (!blacklistOption) {
      // 扩大搜索范围，查找所有可能的菜单项
      const allPossibleSelectors = [
        '[role="button"]',
        '.woo-pop-item-main',
        '.woo-pop-content *',
        '[class*="pop"] *',
        '[class*="PopCard"] *',
        '.popcard *',
        'div[role="button"]',
        'span[role="button"]',
        'a[role="button"]',
        '.woo-pop-item',
        '[class*="menu"] *',
        '[class*="dropdown"] *'
      ];

      console.log('Trying expanded search for blacklist option...');

      // 尝试每个选择器
      for (const selector of allPossibleSelectors) {
        try {
          const elements = Array.from(document.querySelectorAll(selector));
          const found = elements.find(element => {
            const text = element.textContent?.trim() || '';
            return BLACKLIST_KEYWORDS.some(keyword => text.includes(keyword));
          });

          if (found) {
            console.log(`Found blacklist option with selector "${selector}":`, found);
            blacklistOption = found;
            break;
          }
        } catch (e) {
          // 忽略无效选择器错误
          console.warn(`Invalid selector: ${selector}`);
        }
      }
    }

    if (!blacklistOption) {
      // 记录所有可用的菜单项用于调试 - 扩大搜索范围
      const debugSelectors = [
        '[role="button"]',
        '.woo-pop-item-main',
        '.woo-pop-content *',
        '[class*="pop"] *',
        '[class*="PopCard"] *',
        '.popcard *',
        'div', // 最广泛的搜索
        'span',
        'a'
      ];

      let allMenuItems = [];
      for (const selector of debugSelectors) {
        try {
          const elements = Array.from(document.querySelectorAll(selector));
          allMenuItems = allMenuItems.concat(elements);
        } catch (e) {
          // 忽略错误
        }
      }

      // 去重并过滤有文本内容的元素
      const uniqueItems = [...new Set(allMenuItems)]
        .filter(item => item.textContent?.trim())
        .map(item => ({
          tagName: item.tagName,
          className: item.className,
          textContent: item.textContent?.trim(),
          role: item.getAttribute('role'),
          element: item
        }));

      console.error('Blacklist option not found. Total elements found:', uniqueItems.length);
      console.error('Available menu items:');
      uniqueItems.forEach((item, index) => {
        console.error(`${index + 1}. Tag: ${item.tagName}, Class: "${item.className}", Text: "${item.textContent}", Role: "${item.role}"`);
      });

      // 也尝试查找所有可见的元素
      const allVisible = Array.from(document.querySelectorAll('*')).filter(el => {
        const style = window.getComputedStyle(el);
        const text = el.textContent?.trim() || '';
        return style.display !== 'none' &&
               style.visibility !== 'hidden' &&
               text.length > 0 &&
               text.length < 100 &&
               !text.includes('\n'); // 排除包含换行的长文本
      });

      console.error('All visible elements with text:');
      allVisible.slice(0, 30).forEach((el, index) => {
        console.error(`${index + 1}. ${el.tagName}.${el.className} = "${el.textContent?.trim()}"`);
      });
      // 尝试最后的方法：直接点击菜单中的位置
      console.log('Attempting direct click on menu items...');

      // 尝试查找菜单容器
      const menuContainers = [
        document.querySelector('.woo-pop-content'),
        document.querySelector('[class*="pop"]'),
        document.querySelector('.popcard'),
        document.querySelector('[class*="PopCard"]')
      ].filter(Boolean);

      if (menuContainers.length > 0) {
        const container = menuContainers[0];
        console.log('Found menu container:', container);

        // 获取容器中的所有可点击元素
        const clickableItems = Array.from(container.querySelectorAll('*')).filter(el => {
          const style = window.getComputedStyle(el);
          return style.display !== 'none' && style.visibility !== 'hidden';
        });

        console.log('Found clickable items:', clickableItems.length);

        // 尝试点击每个元素
        let clicked = false;
        for (const item of clickableItems) {
          const text = item.textContent?.trim() || '';
          if (BLACKLIST_KEYWORDS.some(keyword => text.includes(keyword))) {
            console.log('Found item with blacklist keyword:', text);
            item.click();
            clicked = true;

            // 等待确认对话框
            setTimeout(() => {
              const dialog = document.querySelector(SELECTORS.CONFIRM_DIALOG);
              if (dialog) {
                console.log('Confirmation dialog appeared after direct click');
                resolve();
              } else {
                reject(new Error('Blacklist option not found'));
              }
            }, 800);
            break;
          }
        }

        if (!clicked) {
          // 如果没有找到包含关键词的元素，尝试点击第一个元素
          if (clickableItems.length > 0) {
            console.log('Clicking first menu item as fallback');
            clickableItems[0].click();

            // 等待确认对话框
            setTimeout(() => {
              const dialog = document.querySelector(SELECTORS.CONFIRM_DIALOG);
              if (dialog) {
                console.log('Confirmation dialog appeared after clicking first item');
                resolve();
              } else {
                reject(new Error('Blacklist option not found'));
              }
            }, 800);
            return;
          }
        }
      } else {
        console.log('No menu container found for direct click');
        reject(new Error('Blacklist option not found'));
      }
      return;
    }

    console.log('Clicking blacklist option:', blacklistOption.textContent);
    blacklistOption.click();

    // 等待确认对话框出现
    waitForElement(SELECTORS.CONFIRM_DIALOG, 3, 400)
      .then(() => {
        console.log('Confirmation dialog appeared');
        resolve();
      })
      .catch(error => {
        console.error('Confirmation dialog did not appear:', error);
        reject(new Error('Confirmation dialog did not appear'));
      });
  });
}

/**
 * 确认拉黑操作
 * @returns {Promise<void>}
 */
export async function confirmBlacklist() {
  return new Promise((resolve, reject) => {
    const confirmButton = findElementByText(SELECTORS.CONFIRM_BUTTONS, CONFIRM_KEYWORDS);

    if (!confirmButton) {
      // 记录所有可用按钮用于调试
      const allButtons = Array.from(document.querySelectorAll('button, .woo-button-main, [class*="dialog"] button'));
      console.error('Confirm button not found. Available buttons:',
        allButtons.map(btn => btn.textContent?.trim()).filter(text => text));
      reject(new Error('Confirm button not found'));
      return;
    }

    console.log('Clicking confirm button:', confirmButton.textContent);
    confirmButton.click();

    // 等待操作完成
    setTimeout(() => {
      console.log('Blacklist action completed successfully');
      resolve();
    }, 1000);
  });
}
