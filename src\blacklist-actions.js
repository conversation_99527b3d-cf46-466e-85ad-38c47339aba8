/**
 * 拉黑操作模块
 * 处理拉黑用户的具体步骤
 */

import { SELECTORS, BLACKLIST_KEYWORDS, CONFIRM_KEYWORDS } from './selectors.js';
import { findElementBySelectors, findElementByText, waitForElement, triggerHover } from './dom-utils.js';

/**
 * 悬停头像显示用户卡片
 * @param {Element} article - 文章元素
 * @returns {Promise<void>}
 */
export async function hoverAvatar(article) {
  return new Promise((resolve, reject) => {
    const avatar = article.querySelector(SELECTORS.AVATAR_ELEMENTS);
    if (!avatar) {
      console.error('Avatar not found in article:', article);
      reject(new Error('Avatar not found'));
      return;
    }

    console.log('Found avatar, triggering hover events...');
    triggerHover(avatar);

    // 等待用户卡片弹出
    let attempts = 0;
    const maxAttempts = 5;

    const checkForUserCard = () => {
      attempts++;
      const userCard = document.querySelector(SELECTORS.USER_CARD_POPUP);

      if (userCard) {
        console.log('User card popup appeared:', userCard);
        resolve();
      } else if (attempts < maxAttempts) {
        console.log(`User card not found, attempt ${attempts}/${maxAttempts}`);
        triggerHover(avatar); // 重新触发悬停
        setTimeout(checkForUserCard, 600);
      } else {
        console.error('User card did not appear after', maxAttempts, 'attempts');
        reject(new Error('User card did not appear'));
      }
    };

    setTimeout(checkForUserCard, 800);
  });
}

/**
 * 点击菜单按钮
 * @returns {Promise<void>}
 */
export async function clickMenuButton() {
  return new Promise((resolve, reject) => {
    const menuButton = findElementBySelectors(SELECTORS.MENU_BUTTONS);

    if (!menuButton) {
      console.error('Menu button not found. Available popup elements:',
        Array.from(document.querySelectorAll('[class*="PopCard"], [class*="pop"], .woo-font--ellipsis')));
      reject(new Error('Menu button not found'));
      return;
    }

    console.log('Clicking menu button:', menuButton);

    // 尝试多种点击方式以确保菜单打开
    menuButton.click();

    // 也尝试触发鼠标事件
    const clickEvent = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window
    });
    menuButton.dispatchEvent(clickEvent);

    // 等待下拉菜单出现 - 增加等待时间和尝试次数
    setTimeout(() => {
      // 尝试多个可能的菜单选择器
      const menuSelectors = [
        '.woo-pop-content',
        '.woo-pop-item-main',
        '[role="button"]',
        '.woo-pop-wrap [role="button"]',
        '[class*="PopCard"] [role="button"]', // 使用通配符匹配PopCard类
        '.woo-pop-item',
        '[class*="pop"] [role="button"]',
        '.popcard [role="button"]' // 通用的popcard类
      ];

      let menuFound = false;
      for (const selector of menuSelectors) {
        const menu = document.querySelector(selector);
        if (menu) {
          console.log('Dropdown menu appeared with selector:', selector, menu);
          menuFound = true;
          break;
        }
      }

      if (menuFound) {
        resolve();
      } else {
        // 记录所有可能的菜单元素用于调试
        const allPossibleMenus = Array.from(document.querySelectorAll('[role="button"], .woo-pop-item-main, .woo-pop-content, [class*="pop"]'));
        console.error('Menu did not appear. Available elements:',
          allPossibleMenus.map(el => ({
            tagName: el.tagName,
            className: el.className,
            textContent: el.textContent?.trim(),
            element: el
          })));
        reject(new Error('Menu did not appear'));
      }
    }, 800); // 增加等待时间到800ms
  });
}

/**
 * 点击拉黑选项
 * @returns {Promise<void>}
 */
export async function clickBlacklistOption() {
  return new Promise((resolve, reject) => {
    const blacklistOption = findElementByText(SELECTORS.BLACKLIST_OPTIONS, BLACKLIST_KEYWORDS);

    if (!blacklistOption) {
      // 记录所有可用的菜单项用于调试
      const allMenuItems = Array.from(document.querySelectorAll('[role="button"], .woo-pop-item-main, .woo-pop-content *'));
      console.error('Blacklist option not found. Available menu items:',
        allMenuItems.map(item => item.textContent?.trim()).filter(text => text));
      reject(new Error('Blacklist option not found'));
      return;
    }

    console.log('Clicking blacklist option:', blacklistOption.textContent);
    blacklistOption.click();

    // 等待确认对话框出现
    waitForElement(SELECTORS.CONFIRM_DIALOG, 3, 400)
      .then(() => {
        console.log('Confirmation dialog appeared');
        resolve();
      })
      .catch(error => {
        console.error('Confirmation dialog did not appear:', error);
        reject(new Error('Confirmation dialog did not appear'));
      });
  });
}

/**
 * 确认拉黑操作
 * @returns {Promise<void>}
 */
export async function confirmBlacklist() {
  return new Promise((resolve, reject) => {
    const confirmButton = findElementByText(SELECTORS.CONFIRM_BUTTONS, CONFIRM_KEYWORDS);

    if (!confirmButton) {
      // 记录所有可用按钮用于调试
      const allButtons = Array.from(document.querySelectorAll('button, .woo-button-main, [class*="dialog"] button'));
      console.error('Confirm button not found. Available buttons:',
        allButtons.map(btn => btn.textContent?.trim()).filter(text => text));
      reject(new Error('Confirm button not found'));
      return;
    }

    console.log('Clicking confirm button:', confirmButton.textContent);
    confirmButton.click();

    // 等待操作完成
    setTimeout(() => {
      console.log('Blacklist action completed successfully');
      resolve();
    }, 1000);
  });
}
