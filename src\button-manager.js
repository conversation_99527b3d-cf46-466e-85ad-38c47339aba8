/**
 * 按钮管理模块
 * 负责创建和管理拉黑按钮
 */

import { SELECTORS, BUTTON_CLASSES } from './selectors.js';
import { BUTTON_STYLES, HOVER_STYLES } from './styles.js';
import { createNotice } from './dom-utils.js';
import { hoverAvatar, clickMenuButton, clickBlacklistOption, confirmBlacklist } from './blacklist-actions.js';

export class ButtonManager {
  constructor() {
    this.blacklistButtons = [];
  }

  /**
   * 添加拉黑按钮到所有关注按钮旁边
   */
  addBlacklistButtons() {
    const followButtons = document.querySelectorAll(SELECTORS.FOLLOW_BUTTON);

    followButtons.forEach((followBtn) => {
      if (followBtn.parentElement && !followBtn.parentElement.querySelector(SELECTORS.BLACKLIST_BUTTON)) {
        this.createBlacklistButton(followBtn);
      }
    });
  }

  /**
   * 创建单个拉黑按钮
   * @param {Element} followBtn - 关注按钮元素
   */
  createBlacklistButton(followBtn) {
    const article = followBtn.closest('article');
    if (!article) return;

    const avatar = article.querySelector(SELECTORS.USERCARD_ATTRIBUTE);
    if (!avatar) return;

    const usercard = avatar.getAttribute('usercard');
    if (!usercard) return;

    const blacklistBtn = this.createButtonElement();
    this.setupButtonEvents(blacklistBtn, usercard, article);
    this.setupAvatarLinkPrevention(article, blacklistBtn);

    // 插入到关注按钮前面
    followBtn.parentElement.insertBefore(blacklistBtn, followBtn);

    this.blacklistButtons.push({
      element: blacklistBtn,
      usercard: usercard
    });
  }

  /**
   * 创建按钮DOM元素
   * @returns {HTMLButtonElement} 按钮元素
   */
  createButtonElement() {
    const blacklistBtn = document.createElement('button');
    blacklistBtn.className = BUTTON_CLASSES;
    
    // 应用样式
    Object.assign(blacklistBtn.style, BUTTON_STYLES);
    
    blacklistBtn.innerHTML = `
      <span class="woo-button-wrap">
        <span class="woo-button-content">
          <i class="woo-font woo-font--close"></i> 拉黑
        </span>
      </span>
    `;

    return blacklistBtn;
  }

  /**
   * 设置按钮事件
   * @param {HTMLButtonElement} blacklistBtn - 拉黑按钮
   * @param {string} usercard - 用户卡片ID
   * @param {Element} article - 文章元素
   */
  setupButtonEvents(blacklistBtn, usercard, article) {
    // 悬停效果
    blacklistBtn.addEventListener('mouseenter', () => {
      Object.assign(blacklistBtn.style, HOVER_STYLES);
    });

    blacklistBtn.addEventListener('mouseleave', () => {
      Object.assign(blacklistBtn.style, BUTTON_STYLES);
    });

    // 点击事件
    blacklistBtn.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
      this.handleBlacklist(usercard, article);
    });
  }

  /**
   * 防止在拉黑过程中点击头像链接
   * @param {Element} article - 文章元素
   * @param {HTMLButtonElement} blacklistBtn - 拉黑按钮
   */
  setupAvatarLinkPrevention(article, blacklistBtn) {
    const avatarLink = article.querySelector(SELECTORS.AVATAR_LINK);
    if (avatarLink) {
      avatarLink.addEventListener('click', (e) => {
        if (blacklistBtn.textContent?.includes('处理中')) {
          e.preventDefault();
          e.stopPropagation();
          console.log('Prevented avatar navigation during blacklist process');
        }
      });
    }
  }

  /**
   * 处理拉黑操作
   * @param {string} usercard - 用户卡片ID
   * @param {Element} article - 文章元素
   */
  async handleBlacklist(usercard, article) {
    console.log(`Starting blacklist process for user: ${usercard}`);

    const blacklistBtn = article.querySelector(SELECTORS.BLACKLIST_BUTTON);
    const originalText = blacklistBtn?.innerHTML;
    
    this.setButtonLoadingState(blacklistBtn);

    try {
      console.log('Step 1: Hovering over avatar...');
      await hoverAvatar(article);

      console.log('Step 2: Clicking menu button...');
      await clickMenuButton();

      console.log('Step 3: Clicking blacklist option...');
      await clickBlacklistOption();

      console.log('Step 4: Confirming blacklist...');
      await confirmBlacklist();

      console.log(`Successfully blacklisted user: ${usercard}`);
      this.hidePost(article);

    } catch (error) {
      console.error('Blacklist process failed:', error);
      this.restoreButtonState(blacklistBtn, originalText);
      this.showErrorMessage(error);
    }
  }

  /**
   * 设置按钮加载状态
   * @param {HTMLButtonElement} blacklistBtn - 拉黑按钮
   */
  setButtonLoadingState(blacklistBtn) {
    if (blacklistBtn) {
      blacklistBtn.innerHTML = `
        <span class="woo-button-wrap">
          <span class="woo-button-content">处理中...</span>
        </span>
      `;
      blacklistBtn.style.pointerEvents = 'none';
    }
  }

  /**
   * 恢复按钮状态
   * @param {HTMLButtonElement} blacklistBtn - 拉黑按钮
   * @param {string} originalText - 原始文本
   */
  restoreButtonState(blacklistBtn, originalText) {
    if (blacklistBtn && originalText) {
      blacklistBtn.innerHTML = originalText;
      blacklistBtn.style.pointerEvents = 'auto';
    }
  }

  /**
   * 显示错误消息
   * @param {Error} error - 错误对象
   */
  showErrorMessage(error) {
    const errorMsg = error && error.message ? error.message : 'Unknown error';
    alert(`拉黑失败: ${errorMsg}\n请手动操作或检查控制台日志`);
  }

  /**
   * 隐藏帖子
   * @param {Element} article - 文章元素
   */
  hidePost(article) {
    article.style.opacity = '0.3';
    article.style.pointerEvents = 'none';
    article.style.position = 'relative';

    const notice = createNotice('已拉黑该用户');
    article.appendChild(notice);
  }

  /**
   * 清理所有按钮
   */
  destroy() {
    this.blacklistButtons.forEach(btn => {
      btn.element.remove();
    });
    this.blacklistButtons = [];
  }
}
