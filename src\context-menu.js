/**
 * 右键菜单系统
 * 在微博文章上添加自定义右键菜单
 */

export class ContextMenu {
  constructor(blacklistStorage, userExtractor) {
    this.blacklistStorage = blacklistStorage;
    this.userExtractor = userExtractor;
    this.menuElement = null;
    this.currentArticle = null;
    
    this.init();
  }

  /**
   * 初始化右键菜单系统
   */
  init() {
    this.createMenuElement();
    this.bindEvents();
    console.log('Context menu system initialized');
  }

  /**
   * 创建菜单DOM元素
   */
  createMenuElement() {
    this.menuElement = document.createElement('div');
    this.menuElement.className = 'weibo-enhancer-context-menu';
    this.menuElement.style.cssText = `
      position: fixed;
      background: white;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      padding: 4px 0;
      z-index: 10000;
      display: none;
      min-width: 120px;
      font-size: 14px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;
    
    document.body.appendChild(this.menuElement);
  }

  /**
   * 绑定事件监听器
   */
  bindEvents() {
    // 监听右键点击
    document.addEventListener('contextmenu', (e) => {
      this.handleContextMenu(e);
    });

    // 监听点击其他地方隐藏菜单
    document.addEventListener('click', (e) => {
      if (!this.menuElement.contains(e.target)) {
        this.hideMenu();
      }
    });

    // 监听滚动隐藏菜单
    document.addEventListener('scroll', () => {
      this.hideMenu();
    });

    // 监听ESC键隐藏菜单
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.hideMenu();
      }
    });
  }

  /**
   * 处理右键菜单事件
   * @param {Event} e - 右键点击事件
   */
  handleContextMenu(e) {
    // 查找最近的微博文章元素
    const article = e.target.closest('article');
    
    if (!article) {
      this.hideMenu();
      return;
    }

    // 检查是否是微博文章
    if (!article.classList.contains('Feed_wrap_3v9LH')) {
      this.hideMenu();
      return;
    }

    e.preventDefault();
    this.currentArticle = article;
    
    // 提取用户信息
    const userInfo = this.userExtractor.extractUserInfo(article);
    if (!userInfo) {
      console.error('Failed to extract user info from article');
      return;
    }

    // 显示菜单
    this.showMenu(e.clientX, e.clientY, userInfo);
  }

  /**
   * 显示右键菜单
   * @param {number} x - 鼠标X坐标
   * @param {number} y - 鼠标Y坐标
   * @param {Object} userInfo - 用户信息
   */
  showMenu(x, y, userInfo) {
    const isBlacklisted = this.blacklistStorage.isBlacklisted(userInfo.userId);
    
    // 创建菜单项
    const menuItems = [];
    
    if (isBlacklisted) {
      menuItems.push({
        text: `取消屏蔽 ${userInfo.username}`,
        icon: '✓',
        action: () => this.unblockUser(userInfo)
      });
    } else {
      menuItems.push({
        text: `屏蔽 ${userInfo.username}`,
        icon: '🚫',
        action: () => this.blockUser(userInfo)
      });
    }

    // 添加其他菜单项
    menuItems.push({
      text: '查看黑名单',
      icon: '📋',
      action: () => this.showBlacklist()
    });

    // 渲染菜单
    this.renderMenu(menuItems);
    
    // 定位菜单
    this.positionMenu(x, y);
    
    // 显示菜单
    this.menuElement.style.display = 'block';
  }

  /**
   * 渲染菜单项
   * @param {Array} menuItems - 菜单项数组
   */
  renderMenu(menuItems) {
    this.menuElement.innerHTML = '';
    
    menuItems.forEach(item => {
      const menuItem = document.createElement('div');
      menuItem.className = 'weibo-enhancer-menu-item';
      menuItem.style.cssText = `
        padding: 8px 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: background-color 0.2s;
      `;
      
      menuItem.innerHTML = `
        <span style="font-size: 12px;">${item.icon}</span>
        <span>${item.text}</span>
      `;
      
      // 悬停效果
      menuItem.addEventListener('mouseenter', () => {
        menuItem.style.backgroundColor = '#f5f5f5';
      });
      
      menuItem.addEventListener('mouseleave', () => {
        menuItem.style.backgroundColor = 'transparent';
      });
      
      // 点击事件
      menuItem.addEventListener('click', (e) => {
        e.stopPropagation();
        item.action();
        this.hideMenu();
      });
      
      this.menuElement.appendChild(menuItem);
    });
  }

  /**
   * 定位菜单
   * @param {number} x - 鼠标X坐标
   * @param {number} y - 鼠标Y坐标
   */
  positionMenu(x, y) {
    const menuRect = this.menuElement.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    // 调整X坐标避免超出视口
    let adjustedX = x;
    if (x + menuRect.width > viewportWidth) {
      adjustedX = viewportWidth - menuRect.width - 10;
    }
    
    // 调整Y坐标避免超出视口
    let adjustedY = y;
    if (y + menuRect.height > viewportHeight) {
      adjustedY = viewportHeight - menuRect.height - 10;
    }
    
    this.menuElement.style.left = `${adjustedX}px`;
    this.menuElement.style.top = `${adjustedY}px`;
  }

  /**
   * 隐藏菜单
   */
  hideMenu() {
    this.menuElement.style.display = 'none';
    this.currentArticle = null;
  }

  /**
   * 屏蔽用户
   * @param {Object} userInfo - 用户信息
   */
  blockUser(userInfo) {
    const success = this.blacklistStorage.addUser(userInfo);
    if (success) {
      this.showNotification(`已屏蔽用户: ${userInfo.username}`, 'success');
      
      // 触发自定义事件通知其他模块
      document.dispatchEvent(new CustomEvent('userBlacklisted', {
        detail: { userInfo, article: this.currentArticle }
      }));
    } else {
      this.showNotification(`屏蔽失败: ${userInfo.username}`, 'error');
    }
  }

  /**
   * 取消屏蔽用户
   * @param {Object} userInfo - 用户信息
   */
  unblockUser(userInfo) {
    const success = this.blacklistStorage.removeUser(userInfo.userId);
    if (success) {
      this.showNotification(`已取消屏蔽: ${userInfo.username}`, 'success');
      
      // 触发自定义事件通知其他模块
      document.dispatchEvent(new CustomEvent('userUnblacklisted', {
        detail: { userInfo, article: this.currentArticle }
      }));
    } else {
      this.showNotification(`取消屏蔽失败: ${userInfo.username}`, 'error');
    }
  }

  /**
   * 显示黑名单
   */
  showBlacklist() {
    const blacklist = this.blacklistStorage.getAllUsers();
    const stats = this.blacklistStorage.getStats();
    
    let message = `黑名单 (${stats.total} 个用户):\n\n`;
    
    if (blacklist.length === 0) {
      message += '暂无屏蔽用户';
    } else {
      blacklist.forEach((user, index) => {
        const addedDate = new Date(user.addedAt).toLocaleDateString();
        message += `${index + 1}. ${user.username} (${addedDate})\n`;
      });
    }
    
    alert(message);
  }

  /**
   * 显示通知
   * @param {string} message - 通知消息
   * @param {string} type - 通知类型 (success/error/info)
   */
  showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'success' ? '#52c41a' : type === 'error' ? '#ff4d4f' : '#1890ff'};
      color: white;
      padding: 12px 20px;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      z-index: 10001;
      font-size: 14px;
      max-width: 300px;
      word-wrap: break-word;
    `;
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    // 3秒后自动移除
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  /**
   * 销毁菜单系统
   */
  destroy() {
    if (this.menuElement && this.menuElement.parentNode) {
      this.menuElement.parentNode.removeChild(this.menuElement);
    }
  }
}
