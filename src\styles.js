/**
 * 样式管理模块
 * 负责注入和管理自定义CSS样式
 */

export function injectStyles() {
  const style = document.createElement('style');
  style.textContent = `
    .blacklist-btn {
      transition: all 0.2s ease !important;
    }

    .blacklist-btn:hover {
      transform: translateY(-1px) !important;
      box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3) !important;
    }

    .blacklist-btn:active {
      transform: translateY(0) !important;
    }
  `;
  document.head.appendChild(style);
  console.log('Custom styles injected');
}

export const BUTTON_STYLES = {
  backgroundColor: '#ff4757',
  borderColor: '#ff4757',
  color: '#fff',
  marginRight: '8px'
};

export const HOVER_STYLES = {
  backgroundColor: '#ff3742',
  borderColor: '#ff3742'
};
