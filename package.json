{"name": "bilibili-cleaner", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:minify": "vite build --minify", "prepare": "husky"}, "dependencies": {"@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@vueuse/core": "^12.8.2", "@vueuse/integrations": "^13.4.0", "p-limit": "^6.2.0", "pinia": "^2.3.1", "universal-cookie": "^7.2.2", "vue": "^3.5.17"}, "devDependencies": {"@eslint/js": "^9.29.0", "@thedutchcoder/postcss-rem-to-px": "^0.0.2", "@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-vue": "^9.33.0", "globals": "^15.15.0", "husky": "^9.1.7", "lint-staged": "^15.5.2", "postcss": "^8.5.6", "prettier": "3.4.2", "prettier-plugin-tailwindcss": "^0.6.13", "stylelint": "^16.21.0", "stylelint-config-standard-scss": "^14.0.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "typescript-eslint": "^8.34.1", "vite": "^6.3.5", "vite-plugin-monkey": "^5.0.9", "vue-tsc": "^2.2.10"}, "lint-staged": {"src/**/*": "prettier --write --ignore-unknown", "src/**/*.{js,mjs,cjs,ts,vue}": "npx eslint --fix", "src/**/*.scss": "npx stylelint --fix"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}