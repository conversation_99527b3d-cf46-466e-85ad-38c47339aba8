// 增大 视频信息字号
html[homepage-increase-rcmd-list-font-size] main {
    .bili-video-card .bili-video-card__info--tit,
    .bili-live-card .bili-live-card__info--tit,
    .single-card.floor-card .title {
        font-size: 16px !important;
    }
    .bili-video-card .bili-video-card__info--bottom,
    .floor-card .sub-title.sub-title {
        font-size: 14px !important;
    }
    .bili-video-card__stats,
    .bili-video-card__stats .bili-video-card__stats--left,
    .bili-video-card__stats .bili-video-card__stats--right {
        font-size: 14px !important;
    }
}

// 移动 负反馈按钮 恢复标题宽度
html[homepage-move-no-interest] main {
    .bili-video-card__info--no-interest {
        top: unset !important;
        bottom: 0 !important;
    }
    .bili-video-card__info--bottom {
        padding-right: 20px !important;
    }
    .bili-video-card.enable-no-interest,
    .bili-live-card.enable-no-interest {
        --title-padding-right: 0;
    }
}

// 隐藏 负反馈按钮 恢复标题宽度
html[homepage-hide-no-interest] main {
    .bili-video-card.enable-no-interest,
    .bili-live-card.enable-no-interest {
        --title-padding-right: 0;
    }
    .bili-video-card__info--no-interest,
    .bili-live-card__info--no-interest {
        display: none !important;
    }
}

// 隐藏 视频tag (已关注/1万点赞)
html[homepage-hide-up-info-icon] main {
    // CSS伪造Logo
    .bili-video-card .bili-video-card__info--icon-text {
        width: 17px;
        height: 17px;
        color: transparent !important;
        background-color: unset !important;
        border-radius: unset !important;
        margin: 0 2px 0 0 !important;
        font-size: 0 !important;
        line-height: unset !important;
        padding: unset !important;
        user-select: none !important;
    }
    .bili-video-card .bili-video-card__info--icon-text::before {
        content: '';
        display: inline-block;
        width: 100%;
        height: 100%;
        background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24" width="24" height="24" fill="currentColor" class="bili-video-card__info--owner__up"><!--[--><path d="M6.15 8.24805C6.5642 8.24805 6.9 8.58383 6.9 8.99805L6.9 12.7741C6.9 13.5881 7.55988 14.248 8.3739 14.248C9.18791 14.248 9.8478 13.5881 9.8478 12.7741L9.8478 8.99805C9.8478 8.58383 10.1836 8.24805 10.5978 8.24805C11.012 8.24805 11.3478 8.58383 11.3478 8.99805L11.3478 12.7741C11.3478 14.41655 10.01635 15.748 8.3739 15.748C6.73146 15.748 5.4 14.41655 5.4 12.7741L5.4 8.99805C5.4 8.58383 5.73578 8.24805 6.15 8.24805z" fill="rgb(148, 153, 160)"></path><path d="M12.6522 8.99805C12.6522 8.58383 12.98795 8.24805 13.4022 8.24805L15.725 8.24805C17.31285 8.24805 18.6 9.53522 18.6 11.123C18.6 12.71085 17.31285 13.998 15.725 13.998L14.1522 13.998L14.1522 14.998C14.1522 15.4122 13.8164 15.748 13.4022 15.748C12.98795 15.748 12.6522 15.4122 12.6522 14.998L12.6522 8.99805zM14.1522 12.498L15.725 12.498C16.4844 12.498 17.1 11.8824 17.1 11.123C17.1 10.36365 16.4844 9.74804 15.725 9.74804L14.1522 9.74804L14.1522 12.498z" fill="rgb(148, 153, 160)"></path><path d="M12 4.99805C9.48178 4.99805 7.283 5.12616 5.73089 5.25202C4.65221 5.33949 3.81611 6.16352 3.72 7.23254C3.60607 8.4998 3.5 10.171 3.5 11.998C3.5 13.8251 3.60607 15.4963 3.72 16.76355C3.81611 17.83255 4.65221 18.6566 5.73089 18.7441C7.283 18.8699 9.48178 18.998 12 18.998C14.5185 18.998 16.7174 18.8699 18.2696 18.74405C19.3481 18.65655 20.184 17.8328 20.2801 16.76405C20.394 15.4973 20.5 13.82645 20.5 11.998C20.5 10.16965 20.394 8.49877 20.2801 7.23205C20.184 6.1633 19.3481 5.33952 18.2696 5.25205C16.7174 5.12618 14.5185 4.99805 12 4.99805zM5.60965 3.75693C7.19232 3.62859 9.43258 3.49805 12 3.49805C14.5677 3.49805 16.8081 3.62861 18.3908 3.75696C20.1881 3.90272 21.6118 5.29278 21.7741 7.09773C21.8909 8.3969 22 10.11405 22 11.998C22 13.88205 21.8909 15.5992 21.7741 16.8984C21.6118 18.7033 20.1881 20.09335 18.3908 20.23915C16.8081 20.3675 14.5677 20.498 12 20.498C9.43258 20.498 7.19232 20.3675 5.60965 20.2392C3.81206 20.0934 2.38831 18.70295 2.22603 16.8979C2.10918 15.5982 2 13.8808 2 11.998C2 10.1153 2.10918 8.39787 2.22603 7.09823C2.38831 5.29312 3.81206 3.90269 5.60965 3.75693z" fill="rgb(148, 153, 160)"></path><!--]--></svg>');
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }
}

// 隐藏 发布时间
html[homepage-hide-video-info-date] main {
    .bili-video-card__info--date {
        display: none !important;
    }
}

// 隐藏 弹幕数
html[homepage-hide-danmaku-count] main {
    .bili-video-card__stats--item:nth-child(2) {
        display: none !important;
    }
}

// 隐藏 稍后再看提示语
html[homepage-hide-bili-watch-later-tip] main {
    .bili-watch-later__tip--lab {
        display: none !important;
    }
}

// 隐藏 视频预览中的弹幕
html[homepage-hide-inline-player-danmaku] main {
    .bpx-player-row-dm-wrap,
    .bpx-player-cmd-dm-wrap {
        display: none !important;
    }
}

// 隐藏 广告
html[homepage-hide-ad-card] main {
    :is(.feed-card, .bili-video-card, .bili-feed-card):not(.bilibili-gate-video-card):has(
            .bili-video-card__info--ad,
            [href*='cm.bilibili.com'],
            .bili-video-card__info--creative-ad,
            .vui_icon.bili-video-card__stats--icon,
            .bili-video-card__info--owner:not([href*='space.bilibili.com'])
        ) {
        display: none !important;
    }
    :is(.feed-card, .bili-video-card, .bili-feed-card):not(:has(.bili-video-card__wrap, .bili-video-card__skeleton)) {
        display: none !important;
    }

    // 布局调整
    .recommended-container_floor-aside .container > *:nth-of-type(5) {
        margin-top: 0 !important;
    }
    .recommended-container_floor-aside .container > *:nth-of-type(6) {
        margin-top: 0 !important;
    }
    .recommended-container_floor-aside .container > *:nth-of-type(7) {
        margin-top: 0 !important;
    }
    .recommended-container_floor-aside .container > *:nth-of-type(n + 8) {
        margin-top: 0 !important;
    }

    // 完全展示10个推荐项
    .recommended-container_floor-aside .container .feed-card:nth-of-type(n + 9) {
        display: inherit !important;
    }
    .recommended-container_floor-aside .container > *:nth-of-type(n + 13) {
        margin-top: 0 !important;
    }
    .recommended-container_floor-aside .container .feed-card:nth-of-type(n + 12) {
        display: inherit !important;
    }
    .recommended-container_floor-aside .container .floor-single-card:first-of-type {
        margin-top: 0 !important;
    }
}

// 隐藏 直播间推荐
html[homepage-hide-live-card-recommend] main {
    .bili-live-card,
    .bili-feed-card:has(.bili-live-card),
    .floor-single-card:has([href^='//live.bilibili.com'], [href^='live.bilibili.com'], [href^='https://live.bilibili.com'])
    {
        display: none !important;
    }
}

// 简化 分区视频推荐
html[homepage-simple-sub-area-card-recommend] main {
    .floor-single-card .layer {
        display: none !important;
    }
    .floor-single-card .floor-card {
        box-shadow: unset !important;
        border: none !important;
    }
    .single-card.floor-card .floor-card-inner {
        &,
        &:hover {
            background: none !important;
        }
    }
}

// 隐藏 分区视频推荐
html[homepage-hide-sub-area-card-recommend] main {
    .floor-single-card:not(:has(.skeleton, .skeleton-item)) {
        display: none !important;
    }
}

// 关闭 视频载入 骨架动效
html[homepage-hide-skeleton-animation] main {
    .bili-video-card .loading_animation {
        .bili-video-card__skeleton--light,
        .bili-video-card__skeleton--text,
        .bili-video-card__skeleton--face,
        .bili-video-card__skeleton--cover {
            &,
            &::after {
                animation: none !important;
            }
        }
    }
    :where(.floor-skeleton, .skeleton) .skeleton-item {
        &,
        &::after {
            animation: none !important;
        }
    }
}

// 隐藏 加载锚点之前的骨架
html[homepage-hide-skeleton-before-anchor] main {
    .bili-video-card:not(.is-rcmd):has(~ .load-more-anchor) {
        display: none !important;
    }
}

// 隐藏 全部加载骨架
html[homepage-hide-skeleton] main {
    .load-more-anchor {
        visibility: hidden;
    }
    .bili-video-card:not(.is-rcmd),
    .floor-single-card:has(.skeleton, .skeleton-item) {
        display: none;
    }
}

// 增大 视频载入 视频数量 (实验功能)
html[homepage-increase-rcmd-load-size] main {
    // 扩增载入后会产生奇怪的骨架空位
    .container > .floor-single-card:has(.skeleton, .skeleton-item, .floor-skeleton) {
        display: none;
    }
}

// 启用 视频列表预加载
html[homepage-rcmd-video-preload] main {
    // 隐藏anchor前的skeleton
    .bili-video-card:not(.is-rcmd):has(~ .load-more-anchor) {
        display: none !important;
    }

    // 隐藏分区视频
    .floor-single-card:not(:has(.skeleton, .skeleton-item)) {
        display: none !important;
    }
    .load-more-anchor.preload {
        position: fixed;
        top: -100px;
        left: -100px;
        opacity: 0;
    }
}
