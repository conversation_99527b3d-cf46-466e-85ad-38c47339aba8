/**
 * DOM选择器常量模块
 * 集中管理所有用到的CSS选择器
 */

export const SELECTORS = {
  // 关注按钮
  FOLLOW_BUTTON: '.follow-btn_followbtn_FNC50',
  
  // 拉黑按钮
  BLACKLIST_BUTTON: '.blacklist-btn',
  
  // 用户卡片相关
  USERCARD_ATTRIBUTE: '[usercard]',
  AVATAR_ELEMENTS: '.woo-avatar-main.woo-avatar-hover, .head_avatar_20c9y',
  AVATAR_LINK: 'a[href*="/u/"]',
  
  // 弹出卡片相关
  USER_CARD_POPUP: '.PopCard_pop_1ghGz, .woo-pop-wrap, [class*="PopCard"]',
  
  // 菜单按钮选择器
  MENU_BUTTONS: [
    '.PopCard_ellipsis_1tfem',
    '.woo-pop-wrap .woo-pop-ctrl',
    '.woo-font--ellipsis',
    '.PopCard_pop_1ghGz .woo-pop-ctrl'
  ],
  
  // 下拉菜单
  DROPDOWN_MENU: '.woo-pop-content, .woo-pop-item-main',
  
  // 拉黑选项选择器
  BLACKLIST_OPTIONS: [
    '.woo-pop-item-main[role="button"]',
    '[role="button"]',
    '.woo-box-flex.woo-box-alignCenter.woo-pop-item-main'
  ],
  
  // 确认对话框
  CONFIRM_DIALOG: '.woo-dialog-btn, .woo-button-main.woo-button-flat, [class*="dialog"]',
  
  // 确认按钮选择器
  CONFIRM_BUTTONS: [
    '.woo-button-main.woo-button-flat.woo-button-primary.woo-button-m.woo-button-round.woo-dialog-btn',
    '.woo-dialog-btn',
    '.woo-button-flat.woo-button-primary',
    'button.woo-dialog-btn'
  ]
};

export const BUTTON_CLASSES = 'woo-button-main woo-button-line woo-button-primary woo-button-s woo-button-round blacklist-btn';

export const BLACKLIST_KEYWORDS = ['加入黑名单', '黑名单', '拉黑', '屏蔽'];
export const CONFIRM_KEYWORDS = ['确定', '确认', 'OK', '确'];
