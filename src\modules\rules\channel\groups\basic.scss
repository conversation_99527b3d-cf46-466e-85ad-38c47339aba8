// 隐藏 横幅banner
html[homepage-hide-banner] {
    .bili-header__banner {
        min-height: unset !important;
        height: 64px !important;
        background: var(--bg1) !important;
    }
    .bili-header__banner > * {
        display: none !important;
    }
    .bili-header__bar {
        box-shadow: 0 2px 4px rgb(128 128 128 / 0.15) !important;
    }

    // 背景色
    html {
        background-color: var(--bg1);
    }

    // icon和文字颜色
    .bili-header .right-entry__outside .right-entry-icon {
        color: var(--text1, #18191c) !important;
    }
    .bili-header .left-entry .entry-title,
    .bili-header .left-entry .download-entry,
    .bili-header .left-entry .default-entry,
    .bili-header .left-entry .loc-entry {
        color: var(--text1, #18191c) !important;
    }
    .bili-header .left-entry .entry-title .zhuzhan-icon {
        color: #00aeec !important;
    }
    .bili-header .right-entry__outside .right-entry-text {
        color: var(--text2, #61666d) !important;
    }

    // header高度
    #biliMainHeader {
        min-height: unset !important;
    }

    // 分区菜单 第一排按钮的二级菜单下置
    .v-popover.is-top {
        padding-top: 5px;
        padding-bottom: unset !important;
        bottom: unset !important;
    }
    @media (width >= 2200px) {
        .v-popover.is-top {
            top: 32px;
        }
    }
    @media (width >= 1701px) and (width <= 2199.9px) {
        .v-popover.is-top {
            top: 32px;
        }
    }
    @media (width >= 1367px) and (width <= 1700.9px) {
        .v-popover.is-top {
            top: 28px;
        }
    }
    @media (width >= 1100px) and (width <= 1366.9px) {
        .v-popover.is-top {
            top: 28px;
        }
    }
    @media (width <= 1099.9px) {
        .v-popover.is-top {
            top: 24px;
        }
    }
}

// 隐藏 全站分区栏
html[channel-hide-subarea] {
    #biliMainHeader {
        min-height: unset !important;
        margin-bottom: 20px !important;
        .bili-header__channel {
            display: none !important;
        }
    }
}

// 隐藏 大图轮播
html[channel-hide-carousel] {
    .banner-carousel,
    .channel-carousel {
        display: none !important;
    }
    .channel-page .channel-page__body .head-cards .head-card {
        display: block !important;
    }
}

// 隐藏 滚动页面时 顶部吸附 顶栏
html[channel-hide-sticky-header] {
    .bili-header__bar.slide-down {
        display: none !important;
    }
}

// 恢复 原始动态按钮
html[homepage-revert-channel-dynamic-icon] {
    .bili-header__channel .channel-icons .icon-bg__dynamic {
        picture,
        svg {
            display: none !important;
        }
        &::after {
            content: '';
            width: 25px;
            height: 25px;
            background-image: url('data:image/svg+xml,<svg width="22" height="23" viewBox="0 0 22 23" fill="none" xmlns="http://www.w3.org/2000/svg" class="icon-bg--icon" data-v-674f5b07=""> <path d="M6.41659 15.625C3.88528 15.625 1.83325 13.7782 1.83325 11.5H10.9999C10.9999 13.7782 8.94789 15.625 6.41659 15.625Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M15.125 16.0827C15.125 18.614 13.2782 20.666 11 20.666L11 11.4993C13.2782 11.4993 15.125 13.5514 15.125 16.0827Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M6.875 6.91667C6.875 9.44797 8.72183 11.5 11 11.5L11 2.33333C8.72182 2.33333 6.875 4.38536 6.875 6.91667Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> <path d="M15.5833 7.375C13.052 7.375 11 9.22183 11 11.5H20.1667C20.1667 9.22183 18.1146 7.375 15.5833 7.375Z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
    }
}
