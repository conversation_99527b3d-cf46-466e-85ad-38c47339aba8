import { Item } from '@/types/item'

export const livePlayerItems: Item[] = [
    {
        type: 'switch',
        id: 'live-page-head-web-player-icon-feedback',
        name: '隐藏 反馈按钮',
        defaultEnable: true,
    },
    {
        type: 'switch',
        id: 'live-page-head-web-player-shop-popover-vm',
        name: '隐藏 购物小橙车提示',
        defaultEnable: true,
    },
    {
        type: 'switch',
        id: 'live-page-head-web-player-awesome-pk-vm',
        name: '隐藏 直播PK特效',
    },
    {
        type: 'switch',
        id: 'live-page-web-player-watermark',
        name: '隐藏 直播水印',
        defaultEnable: true,
    },
    {
        type: 'switch',
        id: 'live-page-head-web-player-announcement-wrapper',
        name: '隐藏 滚动礼物通告',
    },
    {
        type: 'switch',
        id: 'live-page-head-web-player-game-id',
        name: '隐藏 幻星互动游戏',
        defaultEnable: true,
    },
    {
        type: 'switch',
        id: 'live-page-head-web-player-research-container',
        name: '隐藏 直播卡顿打分',
        defaultEnable: true,
    },
    {
        type: 'switch',
        id: 'live-page-head-web-player-live-lottery',
        name: '隐藏 天选时刻',
    },
    {
        type: 'switch',
        id: 'live-page-combo-danmaku',
        name: '隐藏 播放器顶部变动计数弹幕',
    },
    {
        type: 'switch',
        id: 'live-page-clean-all-danmaku-small-emoji',
        name: '隐藏 弹幕中的小表情',
    },
    {
        type: 'switch',
        id: 'live-page-clean-all-danmaku-big-emoji',
        name: '隐藏 弹幕中的大表情',
    },
    {
        type: 'switch',
        id: 'live-page-gift-control-vm',
        name: '隐藏 礼物栏',
    },
    {
        type: 'switch',
        id: 'live-page-fullscreen-danmaku-vm',
        name: '全屏时 隐藏弹幕发送框',
    },
]
