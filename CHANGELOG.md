# CHANGELOG

## 4.3.6

- 修复：番剧页 播放器净化功能缺失问题
- 优化：首页插件兼容性
- 新增：播放器 全屏时显示底边mini进度
- 更新：部分功能细节

## 4.3.5

- 修复：二级评论过滤异常

## 4.3.4

- 新增：评论区 隐藏小表情
- 修复：直播页 隐藏播放器弹幕中的表情
- 优化：关键词过滤器自动处理ASCII全角字符
- 优化：BV号过滤权限高于白名单
- 优化：核心过滤器
- 更新：部分功能细节

## 4.3.3

- 修复：首页视频列表多项相关功能 适配页面变化
- 修复：动态页 隐藏热搜
- 修复：首页 视频过滤
- 更新：首页 隐藏直播间推荐
- 更新：动态页、消息页夜间模式使用官方背景
- 更新：部分功能细节

## 4.3.2

- 修复：稍后再看/收藏夹播放页右键屏蔽失效
- 修复：直播间偶发的样式丢失问题
- 新增：直播间 隐藏XXX来了
- 优化：直播间 隐藏互动框
- 更新：直播间弹幕净化
- 更新：部分功能细节

## 4.3.1

- 新增：动态过滤对顶栏动态列表生效
- 新增：相同域名下同步多标签页夜间模式
- 优化：夜间模式
- 修复：部分直播间样式冲突

## 4.3.0

- 新增：夜间模式
- 优化：隐藏banner适配夜间模式
- 更新：优化网页全屏滚动、全屏滚动功能
- 更新：部分功能细节
- 更新：美化滚动条不再默认启用
- 更新：快捷按钮
- 更新：插件菜单

## 4.2.7

- 新增：普通彩色弹幕变白色
- 优化：动态页 隐藏左右栏
- 优化：动态页 中栏宽度调节
- 优化：热门页 布局样式

## 4.2.6

- 修复：彩色渐变弹幕变白色失效问题
- 新增：搜索页 隐藏加载骨架
- 更新：URL净化

## 4.2.5

- 新增：自动展开弹幕列表
- 移除：旧版分区页相关功能
- 更新：部分功能细节

## 4.2.4

- 修复：搜索页视频过滤失效问题
- 更新：隐藏热搜
- 优化：直播页样式
- 优化：搜索页右键单击处理

## 4.2.3

- 修复：首页插件冲突
- 更新：首页侧栏净化
- 更新：部分功能细节

## 4.2.2

- 修复：播放页 字幕样式适配页面变化
- 更新：播放页 全屏下同时再看人数弹幕数量样式
- 移除：过时的评论区样式，不再适配旧版评论区
- 更新：部分功能细节

## 4.2.1

- 修复：新版分区页 插件快捷按钮
- 更新：全站通用项适配新版分区页

## 4.2.0

- 新增：新版分区页 页面净化优化、功能调整
- 新增：新版分区页 布局调节
- 新增：新版分区页 视频过滤、功能调整
- 新增：动态页 隐藏UP主列表
- 更新：全屏滚动功能
- 更新：部分功能细节

## 4.1.10

- 新增：动态过滤 按类型过滤
- 新增：搜索页 隐藏课堂
- 移除：笔记查看大图优化 适配页面变化
- 更新：动态屏蔽
- 更新：部分功能细节

## 4.1.9

- 新增：播放页 隐藏UP主用户卡片
- 新增：顶栏搜索框隐藏搜索按钮
- 新增：全屏下 显示同时再看人数、弹幕数量
- 新增：全屏滚动时 在视频底部显示顶栏
- 回退：隐藏推荐搜索，解决多插件冲突问题

## 4.1.8

- 新增：播放页 自动展开相关视频
- 新增：空间页 动态过滤
- 修复：全屏滚动与小窗播放器调节冲突问题
- 修复：隐藏推荐搜索

## 4.1.7

- 修复：番剧页 顶栏按钮闪现问题
- 更新：直播页 部分功能细节
- 移除：cn-greasyfork 源

## 4.1.6

- 修复：顶栏左侧被隐藏按钮闪现问题
- 新增：顶栏搜索框与左侧距离调节
- 更新：直播页 部分功能细节
- 新增：cn-greasyfork 源

## 4.1.5

- 新增：Github 源，JSDelivr 分发
- 移除：过时的插件适配
- 修复：首页 隐藏顶部吸附顶栏插件冲突

## 4.1.4

- 移除：旧版顶栏适配
- 修复：暂停播放时弹幕回弹问题
- 修复：视频进度条预览卡顿问题
- 新增：播放页 隐藏展开按钮
- 更新：顶栏左侧、右侧净化相关样式
- 优化：CSS性能

## 4.1.3

- 更新：直播页 禁用播放器皮肤 夜间模式例外处理
- 更新：直播页 部分功能细节

## 4.1.2

- 新增：播放页 使用短域名分享
- 新增：活动播放页 支持播放器相关净化功能
- 新增：视频过滤 BV号过滤适配新版空间页
- 优化：iframe页面适配
- 更新：隐藏视频卡片稍后再看按钮 移入全站通用项
- 更新：评论过滤 适配新版空间页，移除旧版适配
- 更新：部分功能细节

## 4.1.1

- 新增：直播页 默认网页全屏播放
- 更新：收录更多AI机器人
- 更新：播放页 右栏净化

## 4.1.0

- 新增：新版空间页 隐藏 侧栏按钮
- 新增：新版空间页 视频卡片 隐藏弹幕、增大字号
- 修复：新版空间页 头像相关样式
- 更新：视频过滤 适配新版空间页
- 更新：项目依赖
- 更新：部分功能细节

## 4.0.12

- 更新：评论区过滤 增强召唤AI机器人屏蔽
- 更新：评论区过滤 收录更多AI机器人
- 更新：正则模式升级为 ius 模式，支持跨行匹配
- 优化：右键菜单样式

## 4.0.11

- 新增：播放页 展开视频合集第二行标题
- 更新：评论过滤 开关设定对全站通用
- 修复：评论过滤 排除自己的评论和@自己的评论

## 4.0.10

- 新增：评论区过滤 过滤@其他用户的无回复评论
- 更新：收录更多AI机器人
- 更新：部分功能细节

## 4.0.9

- 新增：搜索页 隐藏搜索结果中的直播
- 修复：下拉列表组件
- 更新：部分功能细节

## 4.0.8

- 修复：播放页 调节播放器宽度 Firefox页面抖动bug
- 更新：播放页 右列功能适配页面变动
- 更新：部分功能细节

## 4.0.7

- 新增：首页 按播放量过滤
- 修复：番剧页 全屏滚动视频位置问题

## 4.0.6

- 更新：播放页 播放器宽度调节相关样式
- 更新：播放页 网页全屏滚动、全屏滚动 支持Firefox
- 更新：部分功能细节

## 4.0.5

- 新增：动态内容关键词过滤
- 新增：播放页 自动展开视频简介
- 更新：首页 边栏按钮屏蔽

## 4.0.4

- 移除：稍后再看列表页 双列模式 适配页面变化
- 新增：稍后再看列表页 增大字号、修改列数、隐藏按钮
- 新增：字幕样式 字重设定
- 新增：弹幕样式 字体和字重设定
- 优化：热门页 双列样式
- 更新：部分功能细节

## 4.0.3

- 新增：首页 隐藏刷新按钮文字提示
- 新增：播放页 恢复分P视频编号 默认开启
- 修复：播放页 视频信息相关功能
- 修复：播放页 展开视频标题
- 优化：拖拽面板性能
- 更新：播放页 隐藏弹幕列表改为默认禁用

## 4.0.2

- 更新：播放页 相关视频功能 适配页面变化
- 更新：播放页 视频过滤 适配页面变化
- 优化：直播页 弹幕栏相关功能
- 修复：空间页 评论过滤

## 4.0.1

- 新增：首页 移动负反馈按钮至底部 并恢复标题宽度
- 新增：播放页 控制栏收起时显示高能进度条
- 优化：插件管理器菜单
- 修复：插件面板输入操作触发页面快捷键bug
- 修复：首页插件冲突问题

## 4.0.0

### V4 版本更新说明

- 本次更新会引入 Vue.js CDN，请确保浏览器能够访问 https://registry.npmmirror.com/
- 若更新后出现严重异常，请回退到最后的 V3 版本，并上报问题
- 更新后，需重新设定一下首页和热门页视频布局列数

### 重构

- UI 重构，选用 vue + tailwind 技术栈，headlessui 风格
- 更换样式注入与控制方案

### 新增

- 部分功能加入补充说明
- 首页 隐藏加载锚点之前的骨架
- 播放页 CC 字幕样式相关设定

### 更新

- 快捷按钮改用 localStorage 存储相关数据
- 小窗播放器有关功能改用 localStorage 存储位置和缩放数据
- 动态宽度设定 改为数值调节模式
- 修复字体功能 移入全站通用项

### 优化

- 优化默认启用的功能
- 首页 吸附顶栏动效
- 首页 视频骨架相关功能

### 修复

- 直播页 网页全屏下隐藏弹幕栏bug
- 顶栏搜索框居中

## 3.11.4

- 新增：评论区过滤 过滤只含@其他用户的评论
- 新增：播放页隐藏成为老粉按钮
- 更新：播放页视频合集相关净化功能，适配网页变化
- 更新：播放页播放器弹窗净化功能
- 修复：隐藏横幅banner滚动页面时跳变问题

## 3.11.3

- 更新：播放页、番剧页、动态页新版评论区过滤触发策略
- 修复：番剧页 网页全屏/全屏滚动 视频位置bug

## 3.11.2

- 更新：播放页 评论区过滤
- 更新：番剧页 评论区过滤适配新版评论区
- 更新：首页 视频预加载
- 修复：隐藏评论区失效
- 修复：播放页 隐藏评论编辑器内占位文字失效
- 修复：关键词过滤器 正则反向引用错误捕获问题
- 修复：隐藏页底footer

## 3.11.1

- 更新：播放页 评论区过滤适配页面变化
- 修复：搜索页 视频过滤偶尔不触发问题

## 3.11.0

- 移除：播放页 接下来播放免过滤、播放结束免过滤
- 新增：播放页 相关视频暂存数据过滤，自动替换接下来播放
- 更新：直播页 弹幕列表过滤功能，适配页面变化
- 更新：动态页 净化功能适配新版评论区
- 更新：动态页 评论过滤适配新版评论区
- 优化：分离评论区净化功能，成为全站通用板块
- 优化：首页 净化功能
- 优化：直播页 净化功能

## 3.10.4

- 修复：番剧页 小窗播放器记录位置
- 优化：播放页 非全屏隐藏弹幕栏、播放器宽度调节
- 新增：播放页 禁用弹幕云屏蔽灰测
- 新增：播放页 隐藏右栏

## 3.10.3

- 新增：番剧播放页 默认宽屏播放
- 修复：分区排行榜视频过滤
- 修复：小窗播放器 记录位置

## 3.10.2

- 新增：空间页 用户主页自动跳转到投稿
- 修复：正则表达式构建问题
- 优化：更名频道页为分区页以消除歧义
- 优化：正则模式由 iv 降级为 iu 模式，减少浏览器适配报错

## 3.10.1

- 新增：直播页隐藏水印
- 更新：动态页、空间页评论区过滤触发策略
- 优化：项目代码
- 优化：功能细节

## 3.10.0

- 重构：过滤器模块，优化项目结构，提升过滤性能
- 新增：隐藏播放器迷你弹窗
- 新增：首页、分区页视频过滤支持发布日期过滤

## 3.9.3

- 修复：部分情况弹幕弹窗屏蔽问题、彩色弹幕变白色问题
- 新增：动态页、空间页支持过滤AI相关、@相关评论
- 新增：隐藏播放器心动弹窗
- 优化：评论过滤器代码

## 3.9.2

- 优化：播放页 弹窗净化
- 新增：播放页 强制隐藏播放器内所有弹窗
- 新增：播放页 评论过滤 支持过滤新版评论区AI相关、@相关评论
- 新增：评论过滤器 按用户等级过滤

## 3.9.1

- 新增：空间页 动态列表净化
- 新增：空间页 动态列表评论区净化
- 新增：空间页 评论过滤
- 优化：统一处理fetch相关功能

## 3.9.0

- 新增：播放页新版评论区 净化优化 适配测试
- 新增：播放页新版评论区 评论过滤 适配测试
- 更新：对播放页/首页/热门页/分区页默认开启UP主过滤

## 3.8.2

- 修复：直播页 弹幕列表高度bug
- 修复：过滤器免过滤功能禁用失效bug
- 新增：直播页 隐藏互动投票

## 3.8.1

- 新增：播放页 禁用新版评论区灰度测试(临时功能)
- 优化：功能名称、功能细节

## 3.8.0

- 新增：动态过滤器
- 新增：动态过滤 用户名过滤
- 新增：动态过滤 标题过滤
- 新增：动态过滤 时长过滤
- 新增：直播页 折叠排行榜/大航海
- 新增：直播页 禁用壁纸
- 优化：评论区 踩/回复只在hover时显示，增加延迟减少跳变
- 优化：顶栏 隐藏活动，搜索框样式

## 3.7.4

- 修复：动态页 隐藏动态右侧饰品
- 新增：播放页/番剧页 全屏时页面可滚动
- 新增：首页 推荐视频预加载下一屏

## 3.7.3

- 新增：直播页 隐藏倒计时互动
- 新增：直播页 隐藏发送框粉丝勋章
- 新增：播放页/番剧页 小窗播放器记忆移动位置
- 优化：直播页 隐藏互动功能改为默认开启
- 优化：直播页 隐藏发送框相关功能样式
- 优化：代码typo更正

## 3.7.2

- 新增：直播页 活动直播页自动跳转普通直播
- 优化：播放页 网页全屏页面滚动
- 更新：首页 隐藏banner，第一排按钮弹出菜单下置

## 3.7.1

- 移除：顶栏右侧稍后再看相关功能，适配网页变动
- 新增：顶栏右侧收藏按钮弹出框，自动选中稍后再看
- 新增：动态页 淡化/隐藏已查看过动态的UP主
- 新增：番剧页 普通播放模式宽度调节
- 新增：播放页 修复/禁用右栏底部吸附
- 修复：播放页 右栏宽度异常
- 优化：重写URL净化、BV号转AV号功能
- 优化：去除播放页网址跳变问题
- 优化：减少URL变化对浏览器历史记录的影响
- 优化：顶栏净化对版权视频页优化

## 3.7.0

- 新增：播放页 播放设定板块
- 新增：默认宽屏播放
- 新增：网页全屏时页面可滚动
- 新增：普通播放模式宽度调节
- 新增：动态页隐藏整个评论区
- 新增：顶栏屏蔽BML
- 更新：直播页顶栏净化
- 优化：网址判断
- 优化：功能细节

## 3.6.4

- 新增：播放页展开完整视频标题
- 修复：交换播放器和标题位置后，宽屏模式遮盖UP主bug
- 优化：优化代码、优化功能细节

## 3.6.3

- 新增：修复字体支持动态详情页
- 新增：直播页隐藏头像饰品
- 新增：隐藏视频清晰度
- 更新：收录更多AI机器人
- 优化：功能细节

## 3.6.2

- 新增：稍后再看列表页，双列布局
- 新增：修复字体（直播页、热门页、空间页、稍后再看）
- 新增：播放页 支持筛选视频结束后推荐视频
- 更新：直播页净化功能，适配网页变动

## 3.6.1

- 新增：动态页隐藏头像框
- 新增：动态页隐藏头像徽章
- 新增：动态页视频过滤
- 移除：动态页隐藏新版反馈，适配网页变动
- 更新：Readme

## 3.6.0

- 新增：动态页评论区过滤
- 新增：热搜话题页评论区过滤
- 新增：动态详情页评论区过滤
- 修复：交换播放器和视频信息位置bug

## 3.5.4

- 更新：首页净化，适配AdGuard和网页变动
- 新增：动态页隐藏评论区投票
- 优化：功能细节

## 3.5.3

- 新增：首页隐藏稍后再看提示语
- 新增：动态页隐藏抽奖动态
- 移除：直播页顶栏右侧动态、签到、互动，适配网页变动
- 移除：版权播放页用手机看选项，适配网页变动

## 3.5.2

- 新增：动态页 交换左栏和右栏位置
- 新增：动态页 双行显示UP主列表
- 新增：动态页 扩增中栏宽度
- 优化：插件菜单相关功能

## 3.5.1

- 优化：视频质量计算参数
- 修复：修复typo，优化代码

## 3.5.0

- 新增：热门/每周必看/排行榜页 时长过滤
- 新增：热门/每周必看/排行榜页 竖屏视频过滤
- 新增：热门/每周必看/排行榜页 视频质量过滤（实验功能）
- 新增：右键菜单中复制链接功能

## 3.4.7

- 优化：播放页视频信息置底
- 修复：视频切换分P后评论过滤失效bug
- 新增：直播页顶栏隐藏关注、电池、客户端

## 3.4.6

- 优化：优化评论区净化规则
- 优化：优化笔记图片查看
- 新增：隐藏播放器内评分总结弹窗
- 新增：隐藏动态页直播通知动态

## 3.4.5

- 新增：搜索页隐藏搜索结果顶部的游戏/热搜话题
- 修复：隐藏顶栏按钮hover时弹出框
- 新增：隐藏动态页被block的充电动态
- 新增：隐藏分区页banner

## 3.4.4

- 新增：首页视频列表隐藏负反馈菜单(三个点)，恢复标题宽度
- 更新：隐藏页底footer适配更多页面
- 新增：首页视频预览隐藏弹幕
- 新增：搜索页隐藏搜索结果顶部的版权作品

## 3.4.3

- 修复：空间页css
- 新增：隐藏页底footer
- 优化：净化功能细节

## 3.4.2

- 修复：点击顶栏logo时弹出其他按钮bug
- 新增：隐藏顶栏按钮hover时弹出框
- 新增：隐藏评论区老粉、原始粉丝Tag

## 3.4.1

- 新增：小窗播放器 隐藏底边进度
- 新增：小窗播放器 隐藏弹幕
- 新增：小窗播放器 滚轮调节缩放

## 3.4.0

- 新增：视频过滤支持空间页（UP主主页、投稿列表、视频合集列表）
- 修复：标题关键词白名单失效bug

## 3.3.4

- 新增：隐藏 评论区用户卡片
- 修复：播放页 隐藏视频信息提示typo
- 优化：页面净化功能细节
- 优化：右键单击的判定

## 3.3.3

- 更新：直播页 隐藏播放器顶部变动计数弹幕 适配网页变动

## 3.3.2

- 更新：播放页 隐藏视频信息适配网页变动
- 新增：播放页 相关视频信息置底
- 优化：提升不开启过滤器时的性能
- 修复：播放页 全屏下滚轮调节音量失效bug

## 3.3.1

- 修复：黑名单编辑器bug

## 3.3.0

- 新增：UP主昵称关键词过滤（首页、热门页、播放页、分区页、搜索页）
- 新增：UP主昵称关键词黑名单
- 新增：播放页隐藏接下来播放
- 更新：净化分享适配新版标题
- 优化：一些页面净化功能细节
- 优化：对播放列表页的支持
- 修复：一些bug

## 3.2.1

- 新增：播放页隐藏UP主头像icon
- 修复：BV号转AV号

## 3.2.0

- 新增：评论区过滤器（用户过滤、评论关键词过滤）
- 新增：评论区过滤支持播放页、番剧页
- 优化：URL净化功能

## 3.1.11

- 修复：修复直播页天选时刻蒙版bug
- 新增：隐藏直播页天选时刻
- 优化：限制panel可拖拽范围在视口内

## 3.1.10

- 新增：隐藏播放结束后视频推荐
- 新增：隐藏直播间播放器内打分
- 新增：投币时不自动点赞 #46
- 优化：直播页顶栏净化适配多种顶栏
- 优化：精简视频过滤代码

## 3.1.9

- 新增：隐藏首页换一换按钮
- 新增：隐藏首页稍后再看侧栏按钮
- 新增：隐藏高能进度条常驻图钉按钮
- 新增：隐藏播放器Hi-Res按钮
- 更新：收录更多AI机器人

## 3.1.8

- 新增：隐藏爆炸特效弹幕
- 新增：隐藏整个评论区 #42
- 优化：直播页页面判断

## 3.1.7

- 新增：视频过滤支持分区页
- 新增：首页恢复原始动态按钮
- 更新：README

## 3.1.6

- 新增：页面净化支持分区页
- 新增：首页、分区页 调节页面两侧宽度

## 3.1.5

- 新增：直播页新增弹幕屏蔽项
- 新增：直播页新增弹幕表情屏蔽项
- 新增：评论区新增屏蔽AI发布的评论
- 修复：直播页隐藏直播信息
- 优化：动态页隐藏话题Tag

## 3.1.4

- 修复：URL匹配适配2024拜年祭
- 修复：快捷按钮bug

## 3.1.3

- 修复：菜单页和关键词列表 阻止滚动链
- 新增：视频过滤器快捷按钮
- 新增：增大 视频载入 视频数量
- 新增：常见视频标题关键词整理

## 3.1.2

- 新增：首页 隐藏下滑浏览提示
- 新增：播放页规则部分适配拜年祭播放器
- 新增：顶栏两端距离调节、搜索栏宽度调节

## 3.1.1

- 新增：播放页 隐藏播放器视频播放效果调查
- 修复：BV号转AV号可能的溢出问题

## 3.1.0

- 新增：视频过滤支持搜索页
- 优化：精简代码

## 3.0.4

- 修改：主题颜色
- 更新：README

## 3.0.3

- 新增：播放页 隐藏投票栏、隐藏评论区用户投票状态
- 新增：播放页 交换视频信息和播放器的顺序

## 3.0.2

- 新增：右键菜单新增 添加UP主到白名单
- 新增：标题关键词 支持正则（使用 `/` 包住，如 `/abc|\d+/`）
- 优化：标题关键词 普通词汇大小写通配
- 优化：关键词编辑器样式

## 3.0.1

- 新增：白名单管理（UP主、标题关键词）
- 新增：白名单 支持首页、播放页、热门页
- 新增：首页 标有已关注Tag的视频免过滤（实验性）
- 新增：播放页 接下来播放 免过滤
- 优化：视频过滤行为
- 修复：一些bug
- 更新：README

## 3.0.0

- 新增：视频过滤器（时长过滤、UP主过滤、标题关键词过滤、BV号过滤）
- 新增：黑名单管理（UP主、标题关键词、BV号）
- 新增：视频过滤 支持首页
- 新增：视频过滤 支持播放页
- 新增：视频过滤 支持播热门页（热门视频、每周必看、排行榜）
- 新增：首页隐藏adblock提示 #21
- 新增：直播页 全屏下隐藏弹幕输入框
- 优化：播放页 隐藏列表时隐藏右侧展开按钮

## 2.3.4

- 优化：动态页 动态过滤、元素隐藏

## 2.3.3

- 新增：动态页 支持隐藏左栏 隐藏稍后再看
- 新增：动态页 自动展开折叠动态
- 新增：动态页 支持过滤动态（隐藏Tag、预约、转发、带货）
- 优化：普通播放页 关闭弹幕栏 支持去黑边(实验性)

## 2.3.2

- 新增：互斥开关
- 优化：N 选 1 的单选选项使用互斥开关（首页布局、热门页布局、顶栏收藏/稍后再看）

## 2.3.1

- 新增：热门/排行榜页 456列布局
- 新增：首页 456列布局
- 优化：优化热门/排行榜页样式
- 优化：panel内条目CSS

## 2.3.0

- 新增：页面适配 热门视频/排行榜页
- 新增：热门视频/排行榜页 支持四列布局
- 新增：播放页 隐藏 打卡弹窗
- 新增：首页 增大视频信息文字字号
- 优化：动态页 评论区选项，动态规则适配动态详情页

## 2.2.3

- 修复：版权视频播放页规则丢失bug，移除补丁
- 优化：CSS注入位置改为html tag内，提升性能和稳定性
- 优化：panel显示方式，同一页面内再次打开panel保留上次出现位置和滚动位置

## 2.2.2

- 新增：顶栏 稍后再看相关选项
- 新增：评论区规则、净化分享、视频列表、直角化 适配playlist(稍后再看/收藏夹)
- 优化：首页 隐藏下载桌面端弹窗 改为默认开启
- 优化：全屏下隐藏弹幕输入框，完整展示章节名
- 优化：用户开启开关时执行功能函数
- 优化：功能名称
- 修复：视频列表增加高度

## 2.2.1

- 新增：全屏下隐藏弹幕栏
- 优化：页面直角化 改为默认不开启

## 2.2.0

- 新增：隐藏 顶栏数字小红点
- 优化：功能列表重新布局，采用分组形式
- 优化：功能名称
- 优化：部分CSS细节

## 2.1.1

- 新增：全局功能开关快捷键 `Alt+B` 和 `Ctrl+Alt+B`，Firefox 浏览器只支持 `Ctrl+Alt+B`
- 新增：直播页 隐藏搜索历史、热搜
- 新增：动态页 隐藏视频警告
- 优化：直播页 页面直角化
- 优化：部分CSS细节
- 优化：选项顺序

## 2.1.0

- 新增：直播页，隐藏 高能用户提示
- 新增：直播页，播放器皮肤 恢复默认配色
- 新增：直播页，隐藏 整个互动框
- 新增：首页，简化分区视频样式
- 新增：首页，关闭 视频载入动效
- 新增：首页，隐藏 视频载入骨架
- 新增：播放页，CC字幕字体
- 新增：播放页，CC字幕描边
- 修复：日志输出顺序
- 优化：首页header渐变
- 优化：页面直角化效果，支持版权视频页
- 优化：页面直角化，合并为通用Group选项
- 优化：选项顺序
- 优化：URL净化

## 2.0.3

- 修复：Firefox+ViolentMonkey合用时的规则载入bug
- 优化：脚本初始化流程

## 2.0.2

- 新增：首页 隐藏 右下角-下载桌面端弹窗
- 修复：动态页 header吸附时的元素位置

## 2.0.1

- 新增：脚本URL元数据绑定Greasyfork
- 新增：通用项 header净化支持旧版本header

## 2.0.0

- 重构：项目重构
- 新增：功能列表分组
- 新增：初次安装默认开启基础功能
- 优化：版权视频页、动态页与播放页的功能同步
- 优化：首页规则
- 优化：动态页规则
- 优化：与 Evolved 黑暗模式的适配
- 优化：CSS注入
- 修复：一些bug
- 文档：完善 README 和 CHANGELOG
- 构建：添加 CI

## 1.1.7

- 修复：顶栏右侧动态/历史按钮展开菜单，选择直播栏目时窗口消失bug

## 1.1.6

- 新增："评论区-笔记图片 查看大图优化"，优化点开笔记图片后单图/多图观看体验

## 1.1.5

- 新增：支持隐藏高赞弹幕前的点赞按钮
- 修复：添加chrome系浏览器在版权视频页小概率规则丢失的补丁
- 优化：优化部分项目CSS功能细节
- 优化：完善log记录

## 1.1.4

- 修复：尝试彻底修复Chrominum系与Firefox浏览器新标签页载入时规则丢失bug

## 1.1.3

- 新增：完善对Firefox的支持
- 修复：typo

## 1.1.2

- 修复：打开新标签页时偶然出现的规则载入不全问题

## 1.1.1

- 新增：通用项-美化页面滚动条
- 优化：版权视频页独有选项，名称后添加突出标注

## 1.1.0

- 新增：支持净化版权视频播放页，版权视频播放页多数开关功能与普通播放页同步，少数功能为独有，该页面功能尚未稳定
- 新增：debug模式与日志记录
- 新增：顶栏净化项
- 移除：“只在hover时显示弹幕栏” 功能
- 优化：开关菜单顺序，项目名称
- 优化：部分CSS语句
