/**
 * DOM操作工具模块
 * 提供通用的DOM查找和操作方法
 */

/**
 * 尝试多个选择器查找元素
 * @param {string[]} selectors - 选择器数组
 * @param {Document|Element} context - 查找上下文，默认为document
 * @returns {Element|null} 找到的第一个元素
 */
export function findElementBySelectors(selectors, context = document) {
  for (const selector of selectors) {
    const element = context.querySelector(selector);
    if (element) {
      console.log('Found element with selector:', selector);
      return element;
    }
  }
  return null;
}

/**
 * 根据文本内容查找元素
 * @param {string[]} selectors - 基础选择器数组
 * @param {string[]} keywords - 关键词数组
 * @param {Document|Element} context - 查找上下文
 * @returns {Element|null} 找到的元素
 */
export function findElementByText(selectors, keywords, context = document) {
  // 先尝试精确选择器
  for (const selector of selectors) {
    const element = context.querySelector(selector);
    if (element && hasAnyKeyword(element.textContent, keywords)) {
      console.log('Found element with selector and text:', selector);
      return element;
    }
  }
  
  // 回退到通用搜索
  const allElements = Array.from(context.querySelectorAll(selectors.join(', ')));
  return allElements.find(element => hasAnyKeyword(element.textContent, keywords)) || null;
}

/**
 * 检查文本是否包含任何关键词
 * @param {string} text - 要检查的文本
 * @param {string[]} keywords - 关键词数组
 * @returns {boolean} 是否包含关键词
 */
export function hasAnyKeyword(text, keywords) {
  if (!text) return false;
  const trimmedText = text.trim();
  return keywords.some(keyword => trimmedText.includes(keyword));
}

/**
 * 等待元素出现
 * @param {string} selector - 选择器
 * @param {number} maxAttempts - 最大尝试次数
 * @param {number} interval - 检查间隔（毫秒）
 * @returns {Promise<Element>} 找到的元素
 */
export function waitForElement(selector, maxAttempts = 5, interval = 400) {
  return new Promise((resolve, reject) => {
    let attempts = 0;
    
    const check = () => {
      attempts++;
      const element = document.querySelector(selector);
      
      if (element) {
        console.log(`Element found: ${selector}`);
        resolve(element);
      } else if (attempts < maxAttempts) {
        console.log(`Element not found, attempt ${attempts}/${maxAttempts}: ${selector}`);
        setTimeout(check, interval);
      } else {
        console.error(`Element not found after ${maxAttempts} attempts: ${selector}`);
        reject(new Error(`Element not found: ${selector}`));
      }
    };
    
    setTimeout(check, interval);
  });
}

/**
 * 触发鼠标悬停事件
 * @param {Element} element - 目标元素
 */
export function triggerHover(element) {
  const hoverEvent = new MouseEvent('mouseenter', {
    bubbles: true,
    cancelable: true,
    view: window
  });
  
  const mouseOverEvent = new MouseEvent('mouseover', {
    bubbles: true,
    cancelable: true,
    view: window
  });
  
  element.dispatchEvent(hoverEvent);
  element.dispatchEvent(mouseOverEvent);
}

/**
 * 创建带样式的通知元素
 * @param {string} text - 通知文本
 * @returns {HTMLElement} 通知元素
 */
export function createNotice(text) {
  const notice = document.createElement('div');
  notice.style.cssText = `
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    z-index: 1000;
    font-size: 14px;
  `;
  notice.textContent = text;
  return notice;
}
