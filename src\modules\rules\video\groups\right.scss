// 优化 右栏底部吸附
html[video-page-right-container-sticky-optimize] {
    .right-container {
        display: flex !important;
        .right-container-inner {
            width: 100% !important;
            position: sticky !important;
            top: unset !important;
            align-self: flex-end !important;
            min-height: calc(100vh - 64px) !important;

            // fix #87, #84
            max-width: 100% !important;
            padding-bottom: 0 !important;
        }
    }

    // 小窗播放器挡住下方视频 #87
    .right-container-inner {
        min-height: calc(100vh - 304px) !important;
        bottom: 240px !important;
    }
    body:has(.mini-player-window:not(.on)) .right-container-inner {
        min-height: calc(100vh - 74px) !important;
        bottom: 10px !important;
    }
}

// 禁用 右栏底部吸附
html[video-page-right-container-sticky-disable] {
    .right-container-inner {
        position: static !important;
    }
}

// 隐藏 广告
html[video-page-hide-right-container-ad] {
    .right-container {
        #slide_ad,
        .video-card-ad-small,
        .video-card-ad-small-inner,
        .video-page-special-card-small {
            display: none !important;
        }
        #reco_list,
        .recommend-list-v1 {
            margin-top: 0 !important;
        }
    }
}

// 隐藏 游戏推荐
html[video-page-hide-right-container-video-page-game-card-small] {
    .right-container .video-page-game-card-small {
        display: none !important;
    }
}

// 自动展开 弹幕列表
html[video-page-unfold-right-container-danmaku] {
    #danmukuBox .bui-collapse-wrap:not(.bui-collapse-wrap-folded) {
        max-height: fit-content !important;
        .bui-collapse-body {
            height: fit-content !important;
        }
        .bpx-player-wraplist,
        .bpx-player-filter-wrap,
        .bpx-player-dm-wrap,
        .bpx-player-dm-container,
        .bui-area,
        .bui-long-list-wrap {
            // unavailable in firefox
            max-height: fit-content !important;
        }
    }
}

// 隐藏 弹幕列表
html[video-page-hide-right-container-danmaku] {
    // 不可使用 display:none 否则播放器宽屏模式下danmukuBox的margin-top失效，导致视频覆盖右侧列表
    #danmukuBox {
        visibility: hidden !important;
        height: 0 !important;
        margin-bottom: 0 !important;
    }
}

// 隐藏 自动连播按钮
html[video-page-hide-right-container-reco-list-next-play-next-button] {
    .right-container .next-play {
        .next-button,
        .continuous-btn {
            display: none !important;
        }
    }
}

// 隐藏 接下来播放
html[video-page-hide-right-container-reco-list-next-play] {
    .right-container .next-play {
        display: none !important;
    }
    .right-container .rec-list {
        margin-top: 0 !important;
    }
}

// 恢复 分P视频 编号
html[video-page-hide-right-container-multi-page-add-counter] {
    .video-pod__list.multip.list {
        counter-reset: section-counter;

        .video-pod__item {
            &::before {
                counter-increment: section-counter;
                content: 'P' counter(section-counter);
                font-size: 15px;
                margin-right: 10px;
                transition: color 0.2s;
            }
            &.active::before,
            &:hover::before {
                color: var(--brand_blue);
            }
        }
    }
}

// 展开 视频合集 第二行标题
html[video-page-right-container-section-unfold-title] {
    .video-pod.video-pod .section .video-pod__item {
        .title {
            height: fit-content !important;
        }
        .title-txt {
            -webkit-line-clamp: 2 !important;
            line-height: 21px !important;
            margin-top: 4px !important;
            margin-bottom: 4px !important;
        }
    }
}

// 优化 视频合集 列表高度
html[video-page-hide-right-container-section-height] {
    .video-sections-content-list,
    .video-pod__body {
        height: fit-content !important;
        max-height: 340px !important;
    }
}

// 隐藏 视频合集 自动连播开关
html[video-page-hide-right-container-section-next-btn] {
    .base-video-sections-v1 .next-button {
        display: none !important;
    }
    .video-sections-head_first-line .first-line-left {
        max-width: 100% !important;
    }
    .video-sections-head_first-line .first-line-title {
        max-width: unset !important;
    }
    .video-sections-head_first-line .first-line-right {
        display: none !important;
    }
    .video-pod__header .auto-play {
        display: none !important;
    }
}

// 隐藏 视频合集 播放量
html[video-page-hide-right-container-section-play-num] {
    .base-video-sections-v1 .play-num {
        display: none !important;
    }
    .video-sections-head_second-line .play-num {
        display: none !important;
    }
    .video-pod__header .total-view {
        display: none !important;
    }
}

// 隐藏 视频合集 简介
html[video-page-hide-right-container-section-abstract] {
    .base-video-sections-v1 .abstract {
        display: none !important;
    }
    .base-video-sections-v1 .second-line_left img {
        display: none !important;
    }
    .video-sections-head_second-line .abstract {
        display: none !important;
    }
    .video-sections-head_second-line .second-line_left img {
        display: none !important;
    }
    .video-pod__header .pod-description-reference {
        display: none !important;
    }
}

// 隐藏 视频合集 订阅合集
html[video-page-hide-right-container-section-subscribe] {
    .base-video-sections-v1 .second-line_right {
        display: none !important;
    }
    .video-sections-head_second-line .second-line_right {
        display: none !important;
    }
    .video-pod__header .subscribe-btn {
        display: none !important;
    }
}

// 相关视频 视频信息置底
html[video-page-right-container-set-info-bottom] {
    :is(.video-page-card-small, .video-page-operator-card-small, .recommend-list-container .video-card)
        .card-box
        .info {
        display: flex !important;
        flex-direction: column !important;
    }
    :is(.video-page-card-small, .video-page-operator-card-small, .recommend-list-container .video-card)
        .card-box
        .info
        .upname {
        margin-top: auto !important;
    }
}

// 隐藏 相关视频 视频时长
html[video-page-hide-right-container-duration] {
    .right-container .card-box .duration {
        display: none !important;
    }

    // 适配watchlater, favlist
    .recommend-list-container .duration {
        display: none !important;
    }
}

// 隐藏 相关视频 UP主
html[video-page-hide-right-container-reco-list-rec-list-info-up] {
    .right-container .info {
        .upname {
            visibility: hidden !important;
        }

        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    // 适配watchlater, favlist
    .recommend-list-container .info {
        .upname {
            display: none !important;
        }

        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
}

// 隐藏 相关视频 播放和弹幕
html[video-page-hide-right-container-reco-list-rec-list-info-plays] {
    .right-container .info {
        .playinfo {
            display: none !important;
        }

        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    // 适配watchlater, favlist
    .recommend-list-container .info .playinfo {
        display: none !important;
    }
    .recommend-list-container .info {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
}

// 隐藏 展开/收起 按钮
html[video-page-hide-right-container-reco-list-rec-footer] {
    .right-container,
    .playlist-container--right {
        .rec-footer {
            display: none !important;
        }
    }
}

// 隐藏 全部相关视频
html[video-page-hide-right-container-reco-list-rec-list] {
    .right-container {
        .rec-list,
        .rec-footer {
            display: none !important;
        }
    }

    // 适配watchlater, favlist
    .playlist-container--right .recommend-list-container {
        display: none !important;
    }
}

// 隐藏 活动banner
html[video-page-hide-right-container-right-bottom-banner] {
    #right-bottom-banner,
    .right-bottom-banner {
        display: none !important;
    }

    // 小窗视频防挡 #87
    body:has(.mini-player-window:not(.on)) .right-container-inner {
        padding-bottom: 10px !important;
    }
}

// 隐藏 直播间推荐
html[video-page-hide-right-container-live] {
    .right-container .pop-live-small-mode {
        display: none !important;
    }

    // 小窗视频防挡 #87
    body:has(.mini-player-window:not(.on)) .right-container-inner {
        padding-bottom: 10px !important;
    }
}

// 隐藏 右栏 (宽屏模式不适用)
html[video-page-hide-right-container] {
    &:not([player-is-wide]) .right-container {
        display: none !important;
    }
}
