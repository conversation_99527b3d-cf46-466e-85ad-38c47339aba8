# Weibo Enhancer

A userscript that enhances Weibo with blacklist functionality, allowing you to quickly blacklist users from hot posts.

## Features

- Adds a "拉黑" (Blacklist) button next to the "关注" (Follow) button on each Weibo post
- Automatically performs the 4-step blacklist process:
  1. Hover over the user's avatar to show user card
  2. Click the menu button on the user card
  3. Click "加入黑名单" (Add to blacklist) option
  4. Confirm the action by clicking "确定" (Confirm)
- Hides blacklisted posts with a visual indicator
- Works on Weibo hot posts page (https://weibo.com/hot/weibo/*)

## Development

This project uses Vite with vite-plugin-monkey and TypeScript.

### Prerequisites

- Node.js (v16 or higher)
- npm

### Setup

1. Install dependencies:
```bash
npm install
```

2. Start development server:
```bash
npm run dev
```

3. Build for production:
```bash
npm run build
```

### Installation

1. Build the project to generate the userscript
2. Install a userscript manager like Tampermonkey or Greasemonkey
3. Install the generated userscript file

## Usage

1. Navigate to a Weibo hot posts page (https://weibo.com/hot/weibo/*)
2. You'll see "拉黑" buttons next to "关注" buttons on each post
3. Click the "拉黑" button to automatically blacklist a user
4. The post will be dimmed and marked as blacklisted

## Technical Details

- Built with TypeScript for type safety
- Uses MutationObserver to handle dynamically loaded content
- Implements proper event handling and DOM manipulation
- Includes error handling and user feedback

## File Structure

- `src/main.ts` - Main userscript implementation
- `vite.config.ts` - Vite configuration with vite-plugin-monkey
- `tmp/` - Test HTML files for development reference
