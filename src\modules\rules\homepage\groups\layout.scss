// 使用 3 列布局
html[homepage-layout-3-column] {
    #i_cecream .recommended-container_floor-aside .container {
        grid-template-columns: repeat(3, 1fr) !important;
    }
}

// 使用 4 列布局
html[homepage-layout-4-column] {
    #i_cecream .recommended-container_floor-aside .container {
        grid-template-columns: repeat(4, 1fr) !important;
    }
}

// 使用 5 列布局
html[homepage-layout-5-column] {
    #i_cecream .recommended-container_floor-aside .container {
        grid-template-columns: repeat(5, 1fr) !important;
    }
}

// 使用 6 列布局
html[homepage-layout-6-column] {
    #i_cecream .recommended-container_floor-aside .container {
        grid-template-columns: repeat(6, 1fr) !important;
    }
}

// 修改 页面两侧边距
html[homepage-layout-padding] {
    .bili-feed4-layout,
    .bili-feed4 .bili-header .bili-header__channel {
        padding: 0 var(--homepage-layout-padding, initial) !important;
        width: 100% !important;
    }
}
