// 隐藏 直播LOGO
html[live-page-header-entry-logo] {
    #main-ctnr a.entry_logo[href="//live.bilibili.com"]
    {
        display: none !important;
    }
    .link-navbar-more .search-bar-ctnr {
        margin: 0 auto !important;
    }
    .pre-hold-nav-logo {
        display: none !important;
    }
}

// 隐藏 首页
html[live-page-header-entry-title] {
    #main-ctnr a.entry-title[href="//www.bilibili.com"]
    {
        display: none !important;
    }
    .link-navbar-more .search-bar-ctnr {
        margin: 0 auto !important;
    }
    #prehold-nav-vm .nav-item:has(a[href="//www.bilibili.com"])
    {
        display: none !important;
    }
}

// 隐藏 直播
html[live-page-header-live] {
    #main-ctnr .dp-table-cell a[name='live'] {
        display: none !important;
    }
    .link-navbar-more .search-bar-ctnr {
        margin: 0 auto !important;
    }
    #prehold-nav-vm .nav-item:has(a[href="//live.bilibili.com"])
    {
        display: none !important;
    }
}

// 隐藏 网游
html[live-page-header-net-game] {
    #main-ctnr .dp-table-cell a[name='网游'] {
        display: none !important;
    }
    .link-navbar-more .search-bar-ctnr {
        margin: 0 auto !important;
    }
    #prehold-nav-vm .nav-item:has(a[href="//live.bilibili.com/p/eden/area-tags?parentAreaId=2&areaId=0"])
    {
        display: none !important;
    }
}

// 隐藏 手游
html[live-page-header-mobile-game] {
    #main-ctnr .dp-table-cell a[name='手游'] {
        display: none !important;
    }
    .link-navbar-more .search-bar-ctnr {
        margin: 0 auto !important;
    }
    #prehold-nav-vm .nav-item:has(a[href="//live.bilibili.com/p/eden/area-tags?parentAreaId=3&areaId=0"])
    {
        display: none !important;
    }
}

// 隐藏 单机游戏
html[live-page-header-standalone-game] {
    #main-ctnr .dp-table-cell a[name='单机游戏'] {
        display: none !important;
    }
    .link-navbar-more .search-bar-ctnr {
        margin: 0 auto !important;
    }
    #prehold-nav-vm .nav-item:has(a[href="//live.bilibili.com/p/eden/area-tags?parentAreaId=6&areaId=0"])
    {
        display: none !important;
    }
}

// 隐藏 虚拟主播
html[live-page-header-standalone-vtuber] {
    #main-ctnr .dp-table-cell a[name='虚拟主播'] {
        display: none !important;
    }
    .link-navbar-more .search-bar-ctnr {
        margin: 0 auto !important;
    }
    #prehold-nav-vm .nav-item:has(a[href="//live.bilibili.com/p/eden/area-tags?parentAreaId=9&areaId=0"])
    {
        display: none !important;
    }
}

// 隐藏 娱乐
html[live-page-header-standalone-entertainment] {
    #main-ctnr .dp-table-cell a[name='娱乐'] {
        display: none !important;
    }
    .link-navbar-more .search-bar-ctnr {
        margin: 0 auto !important;
    }
    #prehold-nav-vm .nav-item:has(a[href="//live.bilibili.com/p/eden/area-tags?parentAreaId=1&areaId=0"])
    {
        display: none !important;
    }
}

// 隐藏 电台
html[live-page-header-standalone-radio] {
    #main-ctnr .dp-table-cell a[name='电台'] {
        display: none !important;
    }
    .link-navbar-more .search-bar-ctnr {
        margin: 0 auto !important;
    }
    #prehold-nav-vm .nav-item:has(a[href="//live.bilibili.com/p/eden/area-tags?parentAreaId=5&areaId=0"])
    {
        display: none !important;
    }
}

// 隐藏 赛事
html[live-page-header-standalone-match] {
    #main-ctnr .dp-table-cell a[name='赛事'] {
        display: none !important;
    }
    .link-navbar-more .search-bar-ctnr {
        margin: 0 auto !important;
    }
    #prehold-nav-vm .nav-item:has(a[href="//live.bilibili.com/p/eden/area-tags?parentAreaId=13&areaId=0"])
    {
        display: none !important;
    }
}

// 隐藏 聊天室
html[live-page-header-standalone-chatroom] {
    #main-ctnr .dp-table-cell a[name='聊天室'] {
        display: none !important;
    }
    .link-navbar-more .search-bar-ctnr {
        margin: 0 auto !important;
    }
    #prehold-nav-vm .nav-item:has(a[href="//live.bilibili.com/p/eden/area-tags?parentAreaId=14&areaId=0"])
    {
        display: none !important;
    }
}

// 隐藏 生活
html[live-page-header-standalone-living] {
    #main-ctnr .dp-table-cell a[name='生活'] {
        display: none !important;
    }
    .link-navbar-more .search-bar-ctnr {
        margin: 0 auto !important;
    }
    #prehold-nav-vm .nav-item:has(a[href="//live.bilibili.com/p/eden/area-tags?parentAreaId=10&areaId=0"])
    {
        display: none !important;
    }
}

// 隐藏 知识
html[live-page-header-standalone-knowledge] {
    #main-ctnr .dp-table-cell a[name='知识'] {
        display: none !important;
    }
    .link-navbar-more .search-bar-ctnr {
        margin: 0 auto !important;
    }
    #prehold-nav-vm .nav-item:has(a[href="//live.bilibili.com/p/eden/area-tags?parentAreaId=11&areaId=0"])
    {
        display: none !important;
    }
}

// 隐藏 帮我玩
html[live-page-header-standalone-helpmeplay] {
    #main-ctnr .dp-table-cell a[name='帮我玩'] {
        display: none !important;
    }
    .link-navbar-more .search-bar-ctnr {
        margin: 0 auto !important;
    }
    #prehold-nav-vm .nav-item:has(
        a[href="//live.bilibili.com/p/eden/area-tags?parentAreaId=301&areaId=0"],
        a[href^="//live.bilibili.com/p/html/play-together-area/"]
    ) {
        display: none !important;
    }
}

// 隐藏 互动玩法
html[live-page-header-standalone-interact] {
    #main-ctnr .dp-table-cell a[name='互动玩法'] {
        display: none !important;
    }
    .link-navbar-more .search-bar-ctnr {
        margin: 0 auto !important;
    }
    #prehold-nav-vm .nav-item:has(a[href="//live.bilibili.com/p/eden/area-tags?parentAreaId=15&areaId=0"])
    {
        display: none !important;
    }
}

// 隐藏 购物
html[live-page-header-standalone-shopping] {
    #main-ctnr .dp-table-cell a[name='购物'] {
        display: none !important;
    }
    .link-navbar-more .search-bar-ctnr {
        margin: 0 auto !important;
    }
    #prehold-nav-vm .nav-item:has(a[href="//live.bilibili.com/p/eden/area-tags?parentAreaId=300&areaId=0"])
    {
        display: none !important;
    }
}

// 隐藏 更多
html[live-page-header-showmore-link] {
    #main-ctnr .showmore-link {
        display: none !important;
    }
    .link-navbar-more .search-bar-ctnr {
        margin: 0 auto !important;
    }
    #prehold-nav-vm .nav-item:last-child {
        display: none !important;
    }
}
